package com.rzl.workbenche.quartz.task;

import com.rzl.workbenche.common.config.CacheConfig;
import com.rzl.workbenche.common.constant.CacheConstants;
import com.rzl.workbenche.common.core.redis.RedisCache;
import com.rzl.workbenche.common.utils.DateTimeUtils;
import com.rzl.workbenche.ops.domain.AgentAnalysis;
import com.rzl.workbenche.ops.mapper.AgentAnalysisMapper;
import com.rzl.workbenche.ops.mapper.OpsAgentMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.*;

/**
 * 告警分析定时任务
 */
@Component("opsAgentAnalysisTask")
@Slf4j
public class OpsAgentAnalysisTask {

    @Autowired
    private AgentAnalysisMapper agentAnalysisMapper;

    @Autowired
    private OpsAgentMapper agentMapper;

    @Autowired
    private RedisCache redisCache;

    public void run(){
        log.info("------------执行告警分析定时任务---------------");
        String sysCode = redisCache.getCacheObject(CacheConstants.SYS_CONFIG_KEY + "sys.business.code").toString();
        List<String> codelists = Arrays.asList(sysCode.split(","));
            codelists.forEach(code->{
                CacheConfig.setDb(code);
                //获取所有系统
                List<String> systems = agentMapper.selectSystem();
                for (String system : systems) {
                    List<AgentAnalysis> analysisList = agentMapper.selectAgentAnalysis(system);
                    agentAnalysisMapper.deleteBySystem(system);
                    analysisList.forEach(agent -> {
                        agent.setDura(DateTimeUtils.getDatePoor(agent.getDuration()));
                        agentAnalysisMapper.insertAgentAnalysis(agent);
                    });
                }
            });
        }
}
