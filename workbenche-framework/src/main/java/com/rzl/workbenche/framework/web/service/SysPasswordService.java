package com.rzl.workbenche.framework.web.service;

import java.util.concurrent.TimeUnit;

import com.rzl.workbenche.common.constant.CacheConstants;
import com.rzl.workbenche.common.constant.Constants;
import com.rzl.workbenche.common.constant.LoginInfoConstants;
import com.rzl.workbenche.common.core.domain.entity.SysUser;
import com.rzl.workbenche.common.core.redis.RedisCache;
import com.rzl.workbenche.common.exception.user.UserPasswordNotMatchException;
import com.rzl.workbenche.common.exception.user.UserPasswordRetryLimitExceedException;
import com.rzl.workbenche.common.utils.MessageUtils;
import com.rzl.workbenche.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import com.rzl.workbenche.framework.manager.AsyncManager;
import com.rzl.workbenche.framework.manager.factory.AsyncFactory;
import com.rzl.workbenche.framework.security.context.AuthenticationContextHolder;

/**
 * 登录密码方法
 *
 * <AUTHOR>
 */
@Component
public class SysPasswordService
{
    @Autowired
    private RedisCache redisCache;

    @Value(value = "${user.password.maxRetryCount}")
    private int maxRetryCount;

    @Value(value = "${user.password.lockTime}")
    private int lockTime;

    /**
     * 登录账户密码错误次数缓存键名
     *
     * @param username 用户名
     * @return 缓存键key
     */
    private String getCacheKey(String username)
    {
        return CacheConstants.PWD_ERR_CNT_KEY + username;
    }

    /**
     * 密码错误登录次数
     * @param username
     * @return
     */
    private String getCountCacheKey(String username)
    {
        return CacheConstants.PWD_ERR_CNT_COUNT_KEY + username;
    }
    public void validate(SysUser user)
    {
        Authentication usernamePasswordAuthenticationToken = AuthenticationContextHolder.getContext();
        String username = usernamePasswordAuthenticationToken.getName();
        String password = usernamePasswordAuthenticationToken.getCredentials().toString();

        Boolean retryCount = redisCache.getCacheObject(getCacheKey(username));
        Integer count = redisCache.getCacheObject(getCountCacheKey(username));
        if (count == null )
        {
            count = 0;
        }

        if (retryCount != null && count>= maxRetryCount)
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL,
                    MessageUtils.message("user.password.retry.limit.exceed", count, getMappedValue(count)), LoginInfoConstants.LOGIN_ERROR));
            redisCache.setCacheObject(getCountCacheKey(username), count);
            throw new UserPasswordRetryLimitExceedException(count, getMappedValue(count));
        }

        if (!matches(user, password))
        {
            count = count + 1;
            redisCache.setCacheObject(getCacheKey(username), false, getMappedValue(count), TimeUnit.MINUTES);
            redisCache.setCacheObject(getCountCacheKey(username), count);
            if (count>=maxRetryCount){
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL,
                        MessageUtils.message("user.password.retry.limit.exceed", count, getMappedValue(count)), LoginInfoConstants.LOGIN_ERROR));
                redisCache.setCacheObject(getCountCacheKey(username), count);
                throw new UserPasswordRetryLimitExceedException(count, getMappedValue(count));
            }
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL,
                    MessageUtils.message("user.password.retry.limit.count", retryCount), LoginInfoConstants.LOGIN_ERROR));
            throw new UserPasswordNotMatchException();
        }
        else
        {
            clearLoginRecordCache(username);
        }
    }

    public boolean matches(SysUser user, String rawPassword)
    {
        return SecurityUtils.matchesPassword(rawPassword, user.getPassword());
    }

    public void clearLoginRecordCache(String loginName)
    {
        if (redisCache.hasKey(getCacheKey(loginName)))
        {
            redisCache.deleteObject(getCacheKey(loginName));
        }
        if (redisCache.hasKey(getCountCacheKey(loginName)))
        {
            redisCache.deleteObject(getCountCacheKey(loginName));
        }
    }
    /**
     * 获取分钟数
     */

        public static int getMappedValue(int number) {
            return number <= 5 ? 20 : 20 + (number - 5) * 10;
        }
}
