package com.rzl.workbenche.framework.aspectj;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.rzl.workbenche.common.annotation.CheckSign;
import com.rzl.workbenche.common.config.CacheConfig;
import com.rzl.workbenche.common.constant.Constants;
import com.rzl.workbenche.common.constant.SignConstants;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.enums.ResponseStatus;
import com.rzl.workbenche.common.exception.GlobalException;
import com.rzl.workbenche.common.filter.PropertyPreExcludeFilter;
import com.rzl.workbenche.common.utils.*;
import com.rzl.workbenche.ops.domain.BusinessDataConfig;
import com.rzl.workbenche.ops.domain.OpsCollectInterface;
import com.rzl.workbenche.ops.domain.vo.DataVo;
import com.rzl.workbenche.ops.mapper.BusinessDataConfigMapper;
import com.rzl.workbenche.ops.mapper.OpsCollectInterfaceMapper;
import com.rzl.workbenche.system.domain.PushLog;
import com.rzl.workbenche.system.mapper.PushLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.BindingResult;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.rzl.workbenche.framework.aspectj.LogAspect.EXCLUDE_PROPERTIES;

@Aspect   //定义一个切面
@Configuration
@Slf4j
public class CheckSignAspect {

    @Autowired
    private OpsCollectInterfaceMapper interfaceMapper;

    @Autowired
    private PushLogMapper pushLogMapper;

    @Autowired
    private BusinessDataConfigMapper configMapper;

    private static final String SYSTEM = "A0001";
    // 定义切点Pointcut
    @Pointcut("@annotation(com.rzl.workbenche.common.annotation.CheckSign)")
    public void excudeService() {
        //定义切点
    }

    @Around("excudeService()")
    public AjaxResult doAround(ProceedingJoinPoint joinPoint) {
        log.info("-------------开始验证签名---------------");
        PushLog pushLog = null;
        try {

            //获取开始时间
            long startTime = System.currentTimeMillis();
            ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = Objects.requireNonNull(sra).getRequest();
            MethodSignature signature = (MethodSignature)joinPoint.getSignature();
            Method method = signature.getMethod();
            CheckSign annotation = method.getAnnotation(CheckSign.class);
            //获取注解参数
            pushLog = getAnno(annotation);
            //获取参数
            setRequestValue(request,joinPoint,pushLog);
            String jsonString = JSON.toJSONString(joinPoint.getArgs()[0]);
            DataVo dataVo = JSONObject.parseObject(jsonString, DataVo.class);
            //设置数据源
            String domain = dataVo.getDomain();
            if (StrUtils.isEmpty(domain)){
                saveLog(pushLog,Constants.FAIL,ResponseStatus.SIGNERROR.getInfo());
                return AjaxResult.msg(ResponseStatus.PARAMERROR.getCode(), ResponseStatus.PARAMERROR.getInfo());
            }else {
                CacheConfig.setDb(domain);
            }
            OpsCollectInterface opsCollectInterface = interfaceMapper.selectByName(request.getRequestURI());
            if (Objects.nonNull(opsCollectInterface) && "0".equals(opsCollectInterface.getStatus())){
                    saveLog(pushLog,Constants.FAIL,ResponseStatus.SERVICEERROR.getInfo());
                    return AjaxResult.msg(ResponseStatus.SERVICEERROR.getCode(), ResponseStatus.SERVICEERROR.getInfo());
            }
            //获取sign参数
            String sign = dataVo.getSign();
            if (StrUtils.isBlank(sign)) {
                saveLog(pushLog,Constants.FAIL,ResponseStatus.SIGNERROR.getInfo());
                return AjaxResult.msg(ResponseStatus.SIGNERROR.getCode(), ResponseStatus.SIGNERROR.getInfo());
            }
            //验签
           if (!isCheck(dataVo)){
               saveLog(pushLog,Constants.FAIL,ResponseStatus.SIGNERROR.getInfo());
               return AjaxResult.msg(ResponseStatus.SIGNERROR.getCode(), ResponseStatus.SIGNERROR.getInfo());
           }
           log.info("---------------------------------验签成功---------------------------------");
            AjaxResult result = (AjaxResult)joinPoint.proceed();
            String code = result.get("code").toString();
            if (Constants.OK.equals(code)){
                pushLog.setStatus(Constants.SUCCESS);
                pushLog.setDuration(System.currentTimeMillis() - startTime);
            }else {
                pushLog.setStatus(Constants.FAIL);
                pushLog.setReason((String) result.get("msg"));
            }
            //设置耗时
            pushLogMapper.insertPushLog(pushLog);
            return result;
        } catch (GlobalException t) {
            saveLog(pushLog,Constants.FAIL,t.getMessage());
            return AjaxResult.msg(ResponseStatus.ERROR.getCode(),t.getMessage());
        } catch (Throwable e) {
            e.printStackTrace();
            saveLog(pushLog,Constants.FAIL,"签名异常");
            return AjaxResult.msg(ResponseStatus.ERROR.getCode(), "签名异常");
        }
    }
    /**
     * 验签
     */
    private boolean isCheck(DataVo dataVo) throws Exception {
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put(SignConstants.SIGN_DOMAIN,dataVo.getDomain());
        paramsMap.put(SignConstants.SIGN_SYSTEM,dataVo.getSystem());
        paramsMap.put(SignConstants.SIGN_DATA,dataVo.getData());
        BusinessDataConfig dataConfig = configMapper.selectBySystem(dataVo.getSystem());
        return RSAUtil.verifySignByPublic(dataConfig.getSysPub(),dataVo.getSign(),RSAUtil.buildSignStr(paramsMap));
    }

    /**
     * 保存日志
     */
    public PushLog getAnno(CheckSign checkSign){
        PushLog pushLog = new PushLog();
        //设置接口名称
        pushLog.setName(checkSign.value());
        //设置采集时间
        pushLog.setPushTime(DateTimeUtils.getNowDate());
        return pushLog;
    }
    /**
     * 获取请求的参数，放到log中
     *
     * @param pushLog 推送日志
     * @throws Exception 异常
     */
    private void setRequestValue(HttpServletRequest request,JoinPoint joinPoint, PushLog pushLog) throws Exception
    {
        String requestMethod = request.getMethod();
        pushLog.setType(requestMethod);
        String params = argsArrayToString(joinPoint.getArgs());
        pushLog.setParam(params);
        JSONObject jsonObject = JSONObject.parseObject(params);
        if (Objects.isNull(jsonObject.get(SignConstants.SIGN_SYSTEM))){
            //系统默认A0001
            pushLog.setSystem(SYSTEM);
        }else {
            pushLog.setSystem(jsonObject.get(SignConstants.SIGN_SYSTEM).toString());
        }


    }
    /**
     * 参数拼装
     */
    private String argsArrayToString(Object[] paramsArray)
    {
        StringBuilder params = new StringBuilder("");
        if (paramsArray != null && paramsArray.length > 0)
        {
            for (Object o : paramsArray)
            {
                if (StrUtils.isNotNull(o) && !isFilterObject(o))
                {
                    try
                    {
                        String jsonObj = JSON.toJSONString(o, excludePropertyPreFilter());
                        params.append(jsonObj);
                        params.append("");
                    }
                    catch (Exception e)
                    {
                        log.info(e.getMessage());
                    }
                }
            }
        }
        return params.toString().trim();
    }
    /**
     * 忽略敏感属性
     */
    public PropertyPreExcludeFilter excludePropertyPreFilter()
    {
        return new PropertyPreExcludeFilter().addExcludes(EXCLUDE_PROPERTIES);
    }

    /**
     * 判断是否需要过滤的对象。
     *
     * @param o 对象信息。
     * @return 如果是需要过滤的对象，则返回true；否则返回false。
     */
    @SuppressWarnings("rawtypes")
    public boolean isFilterObject(final Object o)
    {
        Class<?> clazz = o.getClass();
        if (clazz.isArray())
        {
            return clazz.getComponentType().isAssignableFrom(MultipartFile.class);
        }
        else if (Collection.class.isAssignableFrom(clazz))
        {
            Collection collection = (Collection) o;
            for (Object value : collection)
            {
                if (value instanceof MultipartFile){
                    return true;
                }
            }
        }
        else if (Map.class.isAssignableFrom(clazz))
        {
            Map map = (Map) o;
            for (Object value : map.entrySet())
            {
                Map.Entry entry = (Map.Entry) value;
                if (entry.getValue() instanceof MultipartFile){
                    return true;
                }

            }
        }
        return o instanceof MultipartFile || o instanceof HttpServletRequest || o instanceof HttpServletResponse
                || o instanceof BindingResult;
    }
    /**
     * 保存日志
     */
    public void saveLog(PushLog pushLog,String status,String reason){
        pushLog.setStatus(status);
        pushLog.setReason(reason);
        pushLogMapper.insertPushLog(pushLog);
    }
}
