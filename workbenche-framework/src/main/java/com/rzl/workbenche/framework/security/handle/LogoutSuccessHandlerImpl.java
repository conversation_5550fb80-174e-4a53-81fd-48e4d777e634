package com.rzl.workbenche.framework.security.handle;

import java.io.IOException;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.rzl.workbenche.common.constant.Constants;
import com.rzl.workbenche.common.constant.LoginInfoConstants;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.core.domain.model.LoginUser;
import com.rzl.workbenche.common.utils.ServletUtils;
import com.rzl.workbenche.common.utils.StrUtils;
import com.rzl.workbenche.framework.manager.AsyncManager;
import com.rzl.workbenche.framework.web.service.TokenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;
import com.alibaba.fastjson2.JSON;
import com.rzl.workbenche.framework.manager.factory.AsyncFactory;

/**
 * 自定义退出处理类 返回成功
 *
 * <AUTHOR>
 */
@Configuration
public class LogoutSuccessHandlerImpl implements LogoutSuccessHandler
{
    @Autowired
    private TokenService tokenService;

    /**
     * 退出处理
     *
     * @return
     */
    @Override
    public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication)
            throws IOException, ServletException
    {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (StrUtils.isNotNull(loginUser))
        {
            String userName = loginUser.getUsername();
            // 删除用户缓存记录
            tokenService.delLoginUser(loginUser.getToken());
            // 记录用户退出日志
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(userName, Constants.LOGOUT, "退出成功", LoginInfoConstants.LOGIN_OUT));
        }
        ServletUtils.renderString(response, JSON.toJSONString(AjaxResult.success("退出成功"),LoginInfoConstants.LOGIN_OUT));
    }
}
