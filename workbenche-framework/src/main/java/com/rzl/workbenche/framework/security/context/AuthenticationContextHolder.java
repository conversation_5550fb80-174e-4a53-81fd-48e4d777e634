package com.rzl.workbenche.framework.security.context;

import org.springframework.security.core.Authentication;

/**
 * 身份验证信息
 *
 * <AUTHOR>
 */
public class AuthenticationContextHolder
{
    private AuthenticationContextHolder() {
    }

    private static final ThreadLocal<Authentication> CONTEXTHOLDER = new ThreadLocal<>();

    public static Authentication getContext()
    {
        return CONTEXTHOLDER.get();
    }

    public static void setContext(Authentication context)
    {
        CONTEXTHOLDER.set(context);
    }

    public static void upload()
    {
        CONTEXTHOLDER.remove();
    }
}
