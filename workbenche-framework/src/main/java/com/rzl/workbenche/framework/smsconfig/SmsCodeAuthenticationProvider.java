package com.rzl.workbenche.framework.smsconfig;


import com.rzl.workbenche.common.constant.Constants;
import com.rzl.workbenche.common.core.redis.RedisCache;
import com.rzl.workbenche.common.utils.StrUtils;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import javax.servlet.http.HttpServletRequest;

/**
 * 短信登陆鉴权 Provider，要求实现 AuthenticationProvider 接口
 *
 */

public class SmsCodeAuthenticationProvider implements AuthenticationProvider {

    private UserDetailsService userDetailsService;



    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        SmsCodeAuthenticationToken authenticationToken = (SmsCodeAuthenticationToken) authentication;

        String mobile = (String) authenticationToken.getPrincipal();


        UserDetails userDetails = userDetailsService.loadUserByUsername(mobile);

        // 此时鉴权成功后，应当重新 new 一个拥有鉴权的 authenticationResult 返回
        SmsCodeAuthenticationToken authenticationResult = new SmsCodeAuthenticationToken(userDetails, userDetails.getAuthorities());

        authenticationResult.setDetails(authenticationToken.getDetails());

        return authenticationResult;
    }

    private void checkSmsCode(String mobile) {
        RedisCache redisCache = (RedisCache) SpringContextUtil.getBean("redisCache");

        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String inputCode = request.getParameter("code");
        String phone = request.getParameter("phone");

        //获取存时间的key
        String keyTime = Constants.VERIFY_SMS_CODE_TIME + phone;
        //获取存要验证码的key
        String verifyKey = Constants.VERIFY_SMS_CODE + phone;

        String smsCode =  (String)redisCache.getCacheObject(verifyKey);
        if(smsCode == null) {
            throw new BadCredentialsException("验证码失效");
        }
        String applyMobile = (String)redisCache.getCacheObject(phone);
        if(StrUtils.isEmpty(applyMobile) || !applyMobile.equals(phone)) {
            throw new BadCredentialsException("手机号码不一致");
        }
        if(inputCode.equals(smsCode)) {
            throw new BadCredentialsException("验证码错误");
        }
    }

    @Override
    public boolean supports(Class<?> authentication) {
        // 判断 authentication 是不是 SmsCodeAuthenticationToken 的子类或子接口
        return SmsCodeAuthenticationToken.class.isAssignableFrom(authentication);
    }

    public UserDetailsService getUserDetailsService() {
        return userDetailsService;
    }

    public void setUserDetailsService(UserDetailsService userDetailsService) {
        this.userDetailsService = userDetailsService;
    }
}
