<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzl.workbenche.system.mapper.SysRegionMapper">

	<resultMap type="SysRegion" id="SysRegionResult">
		<result property="regId"     column="reg_id"     />
		<result property="parentId"   column="parent_id"   />
		<result property="ancestors"  column="ancestors"   />
		<result property="regName"   column="reg_name"   />
		<result property="regOrder"   column="reg_order"   />
		<result property="regCode"     column="reg_code"      />
		<result property="pregName"      column="preg_name"       />
		<result property="prvName"      column="prv_name"       />
		<result property="status"     column="status"      />
		<result property="delFlag"    column="del_flag"    />
		<result property="parentName" column="parent_name" />
		<result property="createBy"   column="create_by"   />
		<result property="createTime" column="create_time" />
		<result property="updateBy"   column="update_by"   />
		<result property="updateTime" column="update_time" />
		<result property="leader" column="leader" />
		<result property="phone" column="phone" />
		<result property="email" column="email" />
	</resultMap>

	<sql id="selectRegionVo">
        select r.reg_id, r.parent_id, r.ancestors, r.reg_name, r.reg_order, r.reg_code, r.preg_name, r.prv_name, r.status, r.del_flag, r.create_by, r.create_time, r.leader, r.phone, r.email
        from sys_region r
    </sql>

	<select id="selectRegionList" parameterType="SysRegion" resultMap="SysRegionResult">
        <include refid="selectRegionVo"/>
        where r.del_flag = '0'
		<if test="regId != null and regId != ''">
			AND reg_id = #{regId}
		</if>
        <if test="parentId != null and parentId != ''">
			AND parent_id = #{parentId}
		</if>
		<if test="regName != null and regName != ''">
			AND reg_name like concat('%', #{regName}, '%')
		</if>
		<if test="status != null and status != ''">
			AND status = #{status}
		</if>
		<!-- 数据范围过滤 -->
		${params.dataScope}
		order by r.parent_id, r.reg_order
    </select>

    <select id="selectRegionListByRoleId" resultType="String">
		select r.reg_id
		from sys_region r
        left join sys_role_region rr on r.reg_id = rr.reg_id
        where rr.role_id = #{roleId}
            <if test="regionCheckStrictly">
              and r.reg_id not in (select r.parent_id from sys_region r inner join sys_role_region rd on r.reg_id = rd.reg_id and rd.role_id = #{roleId})
            </if>
		order by r.parent_id, r.reg_order
	</select>

    <select id="selectRegionById" resultMap="SysRegionResult">
		select r.reg_id, r.parent_id, r.ancestors, r.reg_name, r.reg_order, r.reg_code, r.preg_name, r.prv_name, r.status, r.leader, r.phone, r.email,
			(select reg_name from sys_region where reg_id = r.parent_id) parent_name
		from sys_region r
		where r.reg_id = #{regionId}
	</select>

    <select id="checkRegionExistUser"  resultType="int">
		select count(1) from sys_user where reg_id = #{regId} and del_flag = '0'
	</select>

	<select id="hasChildByRegionId"  resultType="int">
		select count(1) from sys_region
		where del_flag = '0' and parent_id = #{regId} limit 1
	</select>

	<select id="selectChildrenRegionById"  resultMap="SysRegionResult">
		select * from sys_region where find_in_set(#{regId}, ancestors)
	</select>

	<select id="selectNormalChildrenRegionById"  resultType="int">
		select count(*) from sys_region where status = 1 and del_flag = '0' and find_in_set(#{regId}, ancestors)
	</select>

	<select id="checkRegionNameUnique" resultMap="SysRegionResult">
	    <include refid="selectRegionVo"/>
		where reg_name=#{regName} and parent_id = #{parentId} and del_flag = '0' limit 1
	</select>
    <select id="selectByRegName" resultMap="SysRegionResult">
		<include refid="selectRegionVo"/>
		where r.reg_name = #{regName}
	</select>
    <select id="selectRegIdByRegName" resultType="java.lang.String">
		select reg_id from sys_region where reg_name like concat('%', #{regName}, '%')
	</select>
	<select id="selectRegIds" resultType="java.lang.String">
		select reg_id from sys_region
	</select>

	<insert id="insertRegion" parameterType="SysRegion">
 		insert into sys_region(
 			<if test="regId != null and regId != ''">reg_id,</if>
 			<if test="parentId != null and parentId != ''">parent_id,</if>
 			<if test="regName != null and regName != ''">reg_name,</if>
 			<if test="ancestors != null and ancestors != ''">ancestors,</if>
 			<if test="regOrder != null">reg_order,</if>
 			<if test="regCode != null and regCode != ''">reg_code,</if>
 			<if test="pregName != null and pregName != ''">preg_name,</if>
 			<if test="prvName != null and prvName != ''">prv_name,</if>
 			<if test="status != null">status,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			<if test="leader != null and leader != ''">leader,</if>
 			<if test="phone != null and phone != ''">phone,</if>
 			<if test="email != null and email != ''">email,</if>
 			create_time
 		)values(
 			<if test="regId != null and regId != ''">#{regId},</if>
 			<if test="parentId != null and parentId != ''">#{parentId},</if>
 			<if test="regName != null and regName != ''">#{regName},</if>
 			<if test="ancestors != null and ancestors != ''">#{ancestors},</if>
 			<if test="regOrder != null">#{regOrder},</if>
 			<if test="regCode != null and regCode != ''">#{regCode},</if>
 			<if test="pregName != null and pregName != ''">#{preg_name},</if>
 			<if test="prvName != null and prvName != ''">#{prvName},</if>
 			<if test="status != null">#{status},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			<if test="leader != null and leader != ''">#{leader},</if>
 			<if test="phone != null and phone != ''">#{phone},</if>
 			<if test="email != null and email != ''">#{email},</if>
 			sysdate()
 		)
	</insert>

	<update id="updateRegion" parameterType="SysRegion">
 		update sys_region
 		<set>
 			<if test="parentId != null">parent_id = #{parentId},</if>
 			<if test="regName != null and regName != ''">reg_name = #{regName},</if>
 			<if test="ancestors != null and ancestors != ''">ancestors = #{ancestors},</if>
 			<if test="regOrder != null">reg_order = #{regOrder},</if>
 			<if test="regCode != null">reg_code = #{regCode},</if>
 			<if test="pregName != null">preg_name = #{pregName},</if>
 			<if test="prvName != null">prv_name = #{prvName},</if>
 			<if test="status != null and status != ''">status = #{status},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			<if test="leader != null">leader = #{leader},</if>
 			<if test="phone != null">phone = #{phone},</if>
 			<if test="email != null">email = #{email},</if>
 			update_time = sysdate()
 		</set>
 		where reg_id = #{regId}
	</update>

	<update id="updateRegionChildren" parameterType="java.util.List">
	    update sys_region set ancestors =
	    <foreach collection="regions" item="item" index="index"
	        separator=" " open="case reg_id" close="end">
	        when #{item.regId} then #{item.ancestors}
	    </foreach>
	    where reg_id in
	    <foreach collection="regions" item="item" index="index"
	        separator="," open="(" close=")">
	        #{item.regId}
	    </foreach>
	</update>

	<update id="updateRegionStatusNormal" >
 	    update sys_region set status = '1' where reg_id in
 	    <foreach collection="array" item="regId" open="(" separator="," close=")">
        	#{regId}
        </foreach>
	</update>

	<delete id="deleteRegionById" >
		update sys_region set del_flag = '2' where reg_id = #{regId}
	</delete>

</mapper>
