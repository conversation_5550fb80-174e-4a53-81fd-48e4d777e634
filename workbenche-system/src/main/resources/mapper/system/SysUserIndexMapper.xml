<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzl.workbenche.system.mapper.SysUserIndexMapper">

    <resultMap type="SysUserIndex" id="SysUserIndexResult">
        <result property="userId"    column="user_id"    />
        <result property="indexId"    column="index_id"    />
        <result property="type"    column="type"    />
        <result property="indexOrder"    column="index_order"    />
        <result property="system"    column="system"    />
    </resultMap>

    <sql id="selectSysUserIndexVo">
        select user_id, index_id, type, index_order, `system` from sys_user_index
    </sql>

    <select id="selectSysUserIndexList" parameterType="SysUserIndex" resultMap="SysUserIndexResult">
        <include refid="selectSysUserIndexVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="indexId != null "> and index_id = #{indexId}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="indexOrder != null "> and index_order = #{indexOrder}</if>
            <if test="system != null  and system != ''"> and `system` = #{system}</if>
        </where>
    </select>

    <select id="selectSysUserIndexByUserId" parameterType="Long" resultMap="SysUserIndexResult">
        <include refid="selectSysUserIndexVo"/>
        where user_id = #{userId}
    </select>
    <select id="selectIndexOrder" resultMap="SysUserIndexResult">
        <include refid="selectSysUserIndexVo"/>
        where user_id = #{userId} and index_id = #{indexId}
    </select>

    <insert id="insertSysUserIndex" parameterType="SysUserIndex">
        insert into sys_user_index
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="indexId != null">index_id,</if>
            <if test="type != null">type,</if>
            <if test="indexOrder != null">index_order,</if>
            <if test="system != null">`system`,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="indexId != null">#{indexId},</if>
            <if test="type != null">#{type},</if>
            <if test="indexOrder != null">#{indexOrder},</if>
            <if test="system != null">#{system},</if>
        </trim>
    </insert>
    <insert id="insertBatch">
        insert into sys_user_index(user_id, index_id, type, index_order, `system`) values
        <foreach item="item" collection="userIndexs" separator="," >
            (#{item.userId},#{item.indexId},#{item.type},#{item.indexOrder},#{item.system})
        </foreach>
    </insert>

    <update id="updateSysUserIndex" parameterType="SysUserIndex">
        update sys_user_index
        <trim prefix="SET" suffixOverrides=",">
            <if test="indexId != null">index_id = #{indexId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="indexOrder != null">index_order = #{indexOrder},</if>
            <if test="system != null">`system` = #{system},</if>
        </trim>
        where user_id = #{userId}
    </update>

    <delete id="deleteSysUserIndexByUserId" parameterType="Long">
        delete from sys_user_index where user_id = #{userId}
    </delete>

    <delete id="deleteSysUserIndexByUserIds" parameterType="String">
        delete from sys_user_index where user_id in
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>
    <delete id="deleteSysUserIndex">
        delete from sys_user_index where user_id = #{userId} and type = #{type}
    </delete>
</mapper>