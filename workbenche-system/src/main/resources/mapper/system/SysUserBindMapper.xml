<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzl.workbenche.system.mapper.SysUserBindMapper">

    <resultMap type="SysUserBind" id="SysUserBindResult">
        <result property="id"    column="id"    />
        <result property="system"    column="system"    />
        <result property="userId"    column="user_id"    />
        <result property="bindUser"    column="bind_user"    />
        <result property="bindParam"    column="bind_param"    />
        <result property="accountType"    column="account_type"    />
        <result property="accountStatus"    column="account_status"    />
        <result property="bindTime"    column="bind_time"    />
        <result property="unBindTime"    column="un_bind_time"    />
        <result property="user"    column="user"    />
        <result property="systemName"    column="systemName"    />
    </resultMap>

    <sql id="selectSysUserBindVo">
        select id, `system`, user_id, bind_user, bind_param, account_type, account_status, bind_time, un_bind_time from sys_user_bind
    </sql>

    <select id="selectSysUserBindList" parameterType="SysUserBind" resultMap="SysUserBindResult">
        select b.id, b.`system`, b.user_id, b.bind_user, b.bind_param, b.account_type, b.account_status, b.bind_time, b.un_bind_time,u.nick_name user ,bc.system_name systemName from sys_user_bind b
        left join sys_user u on b.user_id = u.user_id
        join business_data_config bc on b.`system` = bc.system_code
        <where>
            <if test="system != null  and system != ''"> and b.`system` = #{system}</if>
            <if test="bindUser != null  and bindUser != ''"> and b.bind_user like concat('%', #{bindUser}, '%')</if>
            <if test="accountStatus != null  and accountStatus != ''"> and b.account_status = #{accountStatus}</if>
            <if test="userId != null  and userId != ''"> and b.user_id = #{userId}</if>
        </where>
    </select>

    <select id="selectSysUserBindById" parameterType="Long" resultMap="SysUserBindResult">
        <include refid="selectSysUserBindVo"/>
        where id = #{id}
    </select>
    <select id="selectSysUserBind" resultMap="SysUserBindResult">
        <include refid="selectSysUserBindVo"/>
        where user_id = #{userId} and `system` = #{system}
    </select>
    <select id="findByUserId" resultMap="SysUserBindResult">
        <include refid="selectSysUserBindVo"/>
        where user_id = #{userId}
    </select>
    <select id="selectSysAndUser" resultMap="SysUserBindResult">
        <include refid="selectSysUserBindVo"/>
        where bind_user = #{bindUser} and `system` = #{system} and account_status = "1"
    </select>
    <select id="selectBySystemAndUserId" resultMap="SysUserBindResult">
        <include refid="selectSysUserBindVo"/>
        where user_id = #{userId} and `system` = #{system} and account_status = "1"
    </select>

    <insert id="insertSysUserBind" parameterType="SysUserBind">
        insert into sys_user_bind
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="system != null">`system`,</if>
            <if test="userId != null">user_id,</if>
            <if test="bindUser != null">bind_user,</if>
            <if test="bindParam != null">bind_param,</if>
            <if test="accountType != null">account_type,</if>
            <if test="accountStatus != null">account_status,</if>
            <if test="bindTime != null">bind_time,</if>
            <if test="unBindTime != null">un_bind_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="system != null">#{system},</if>
            <if test="userId != null">#{userId},</if>
            <if test="bindUser != null">#{bindUser},</if>
            <if test="bindParam != null">#{bindParam},</if>
            <if test="accountType != null">#{accountType},</if>
            <if test="accountStatus != null">#{accountStatus},</if>
            <if test="bindTime != null">#{bindTime},</if>
            <if test="unBindTime != null">#{unBindTime},</if>
        </trim>
    </insert>
    <insert id="saveBatch">
        insert into sys_user_bind(`system`, user_id, bind_user, bind_param, account_type, account_status, bind_time, un_bind_time)
        values
            <foreach collection="list" item="item" separator=",">
                (#{item.system},#{item.userId},#{item.bindUser},#{item.bindParam},#{item.accountType},#{item.accountStatus},#{item.bindTime},#{item.unBindTime})
            </foreach>
    </insert>

    <update id="updateSysUserBind" parameterType="SysUserBind">
        update sys_user_bind
        <trim prefix="SET" suffixOverrides=",">
            <if test="system != null">`system` = #{system},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="bindUser != null">bind_user = #{bindUser},</if>
            <if test="bindParam != null">bind_param = #{bindParam},</if>
            <if test="accountType != null">account_type = #{accountType},</if>
            <if test="accountStatus != null">account_status = #{accountStatus},</if>
            <if test="bindTime != null">bind_time = #{bindTime},</if>
            <if test="unBindTime != null">un_bind_time = #{unBindTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysUserBindById" parameterType="Long">
        delete from sys_user_bind where id = #{id}
    </delete>

    <delete id="deleteSysUserBindByIds" parameterType="String">
        delete from sys_user_bind where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
