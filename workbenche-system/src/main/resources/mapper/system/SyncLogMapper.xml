<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzl.workbenche.system.mapper.SyncLogMapper">

    <resultMap type="SyncLog" id="SyncLogResult">
        <result property="id"    column="id"    />
        <result property="param"    column="param"    />
        <result property="syncTime"    column="sync_time"    />
        <result property="status"    column="status"    />
        <result property="duration"    column="duration"    />
        <result property="name"    column="name"    />
        <result property="type"    column="type"    />
        <result property="system"    column="system"    />
        <result property="reason"    column="reason"    />
    </resultMap>

    <sql id="selectSyncLogVo">
        select id, param, sync_time, status, duration, name, type, `system`, reason from sync_log
    </sql>

    <select id="selectSyncLogList" parameterType="SyncLog" resultMap="SyncLogResult">
        <include refid="selectSyncLogVo"/>
        <where>
            <if test="syncTime != null "> and sync_time = #{syncTime}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="name != null  and name != ''"> and `name` like concat('%', #{name}, '%')</if>
            <if test="system != null  and system != ''"> and `system` = #{system}</if>
        </where>
    </select>

    <select id="selectSyncLogById" parameterType="Long" resultMap="SyncLogResult">
        <include refid="selectSyncLogVo"/>
        where id = #{id}
    </select>

    <insert id="insertSyncLog" parameterType="SyncLog" useGeneratedKeys="true" keyProperty="id">
        insert into sync_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="param != null">param,</if>
            <if test="syncTime != null">sync_time,</if>
            <if test="status != null">status,</if>
            <if test="duration != null">duration,</if>
            <if test="name != null">`name`,</if>
            <if test="type != null">`type`,</if>
            <if test="system != null">`system`,</if>
            <if test="reason != null">reason,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="param != null">#{param},</if>
            <if test="syncTime != null">#{syncTime},</if>
            <if test="status != null">#{status},</if>
            <if test="duration != null">#{duration},</if>
            <if test="name != null">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="system != null">#{system},</if>
            <if test="reason != null">#{reason},</if>
        </trim>
    </insert>

    <update id="updateSyncLog" parameterType="SyncLog">
        update sync_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="param != null">param = #{param},</if>
            <if test="syncTime != null">sync_time = #{syncTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="name != null">`name` = #{name},</if>
            <if test="type != null">`type` = #{type},</if>
            <if test="system != null">`system` = #{system},</if>
            <if test="reason != null">reason = #{reason},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSyncLogById" parameterType="Long">
        delete from sync_log where id = #{id}
    </delete>

    <delete id="deleteSyncLogByIds" parameterType="String">
        delete from sync_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>