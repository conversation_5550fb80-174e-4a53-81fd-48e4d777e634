<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzl.workbenche.system.mapper.PushLogMapper">

    <resultMap type="PushLog" id="PushLogResult">
        <result property="id"    column="id"    />
        <result property="param"    column="param"    />
        <result property="pushTime"    column="push_time"    />
        <result property="status"    column="status"    />
        <result property="duration"    column="duration"    />
        <result property="name"    column="name"    />
        <result property="type"    column="type"    />
        <result property="system"    column="system"    />
        <result property="reason"    column="reason"    />
    </resultMap>

    <sql id="selectPushLogVo">
        select id, param, push_time, status, duration,type, `system`,name,reason from push_log
    </sql>

    <select id="selectPushLogList" parameterType="PushLog" resultMap="PushLogResult">
        <include refid="selectPushLogVo"/>
        <where>
            <if test="pushTime != null "> and date_format(push_time,'%y%m%d') = date_format(#{pushTime},'%y%m%d')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="name != null  and name != ''"> and `name` like concat('%', #{name}, '%')</if>
            <if test="type != null  and type != ''"> and  `type` = #{type}</if>
            <if test="system != null  and system != ''"> and `system` = #{system}</if>
        </where>
    </select>

    <select id="selectPushLogById" parameterType="Long" resultMap="PushLogResult">
        <include refid="selectPushLogVo"/>
        where id = #{id}
    </select>

    <insert id="insertPushLog" parameterType="PushLog" useGeneratedKeys="true" keyProperty="id">
        insert into push_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="param != null">param,</if>
            <if test="pushTime != null">push_time,</if>
            <if test="status != null">status,</if>
            <if test="duration != null">duration,</if>
            <if test="name != null">`name`,</if>
            <if test="type != null">`type`,</if>
            <if test="system != null">`system`,</if>
            <if test="reason != null">reason,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="param != null">#{param},</if>
            <if test="pushTime != null">#{pushTime},</if>
            <if test="status != null">#{status},</if>
            <if test="duration != null">#{duration},</if>
            <if test="name != null">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="system != null">#{system},</if>
            <if test="reason != null">#{reason},</if>
        </trim>
    </insert>

    <update id="updatePushLog" parameterType="PushLog">
        update push_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="param != null">param = #{param},</if>
            <if test="pushTime != null">push_time = #{pushTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="name != null">`name` = #{name},</if>
            <if test="type != null">`type` = #{type},</if>
            <if test="system != null">`system` = #{system},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePushLogById" parameterType="Long">
        delete from push_log where id = #{id}
    </delete>

    <delete id="deletePushLogByIds" parameterType="String">
        delete from push_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
