<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzl.workbenche.system.mapper.SysEnumPwdMapper">

    <resultMap type="SysEnumPwd" id="SysEnumPwdResult">
        <result property="id"    column="id"    />
        <result property="pwd"    column="pwd"    />
        <result property="pwdType"    column="pwd_type"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectSysEnumPwdVo">
        select id, pwd, pwd_type, create_time, create_by, update_time, update_by from sys_enum_pwd
    </sql>

    <select id="selectSysEnumPwdList" parameterType="SysEnumPwd" resultMap="SysEnumPwdResult">
        <include refid="selectSysEnumPwdVo"/>
        <where>
            <if test="pwd != null  and pwd != ''"> and pwd like concat('%', #{pwd}, '%')</if>
            <if test="pwdType != null "> and pwd_type = #{pwdType}</if>
        </where>
    </select>

    <select id="selectSysEnumPwdById" parameterType="Long" resultMap="SysEnumPwdResult">
        <include refid="selectSysEnumPwdVo"/>
        where id = #{id}
    </select>

    <insert id="insertSysEnumPwd" parameterType="SysEnumPwd" useGeneratedKeys="true" keyProperty="id">
        insert into sys_enum_pwd
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pwd != null">pwd,</if>
            <if test="pwdType != null">pwd_type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pwd != null">#{pwd},</if>
            <if test="pwdType != null">#{pwdType},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateSysEnumPwd" parameterType="SysEnumPwd">
        update sys_enum_pwd
        <trim prefix="SET" suffixOverrides=",">
            <if test="pwd != null">pwd = #{pwd},</if>
            <if test="pwdType != null">pwd_type = #{pwdType},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysEnumPwdById" parameterType="Long">
        delete from sys_enum_pwd where id = #{id}
    </delete>

    <delete id="deleteSysEnumPwdByIds" parameterType="String">
        delete from sys_enum_pwd where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
