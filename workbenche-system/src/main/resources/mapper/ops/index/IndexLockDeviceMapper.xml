<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzl.workbenche.ops.mapper.index.IndexLockDeviceMapper">

    <resultMap type="IndexLockDevice" id="IndexLockDeviceResult">
        <result property="id"    column="id"    />
        <result property="system"    column="system"    />
        <result property="prvId"    column="prv_id"    />
        <result property="prvName"    column="prv_name"    />
        <result property="pregId"    column="preg_id"    />
        <result property="pregName"    column="preg_name"    />
        <result property="regId"    column="reg_id"    />
        <result property="regName"    column="reg_name"    />
        <result property="deviceName"    column="device_name"    />
        <result property="deviceType"    column="device_type"    />
        <result property="deviceAddress"    column="device_address"    />
        <result property="addTime"    column="add_time"    />
        <result property="lockNum"    column="lock_num"    />
    </resultMap>

    <sql id="selectIndexLockDeviceVo">
        select id, `system`, prv_id, prv_name, preg_id, preg_name, reg_id, reg_name, device_name, device_type, device_address, add_time, lock_num from index_lock_device
    </sql>

    <select id="selectIndexLockDeviceList" parameterType="IndexLockDevice" resultMap="IndexLockDeviceResult">
        <include refid="selectIndexLockDeviceVo"/>
        <where>
            <if test="system != null  and system != ''"> and `system` = #{system}</if>
            <if test="prvId != null  and prvId != ''"> and prv_id = #{prvId}</if>
            <if test="prvName != null  and prvName != ''"> and prv_name like concat('%', #{prvName}, '%')</if>
            <if test="pregId != null  and pregId != ''"> and preg_id = #{pregId}</if>
            <if test="pregName != null  and pregName != ''"> and preg_name like concat('%', #{pregName}, '%')</if>
            <if test="regId != null  and regId != ''"> and reg_id = #{regId}</if>
            <if test="regName != null  and regName != ''"> and reg_name like concat('%', #{regName}, '%')</if>
            <if test="deviceName != null  and deviceName != ''"> and device_name like concat('%', #{deviceName}, '%')</if>
            <if test="deviceType != null  and deviceType != ''"> and device_type = #{deviceType}</if>
            <if test="deviceAddress != null  and deviceAddress != ''"> and device_address = #{deviceAddress}</if>
            <if test="addTime != null  and addTime != ''"> and add_time = #{addTime}</if>
            <if test="lockNum != null  and lockNum != ''"> and lock_num = #{lockNum}</if>
        </where>
    </select>

    <select id="selectIndexLockDeviceById" parameterType="Long" resultMap="IndexLockDeviceResult">
        <include refid="selectIndexLockDeviceVo"/>
        where id = #{id}
    </select>
    <select id="selectStatistics" resultType="com.rzl.workbenche.ops.domain.vo.CardStatisticsVo">
        select count(*) deviceNum,SUM(IFNULL(lock_num,0)) lockNum FROM index_lock_device
    </select>

    <insert id="insertIndexLockDevice" parameterType="IndexLockDevice" useGeneratedKeys="true" keyProperty="id">
        insert into index_lock_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="system != null">`system`,</if>
            <if test="prvId != null">prv_id,</if>
            <if test="prvName != null">prv_name,</if>
            <if test="pregId != null">preg_id,</if>
            <if test="pregName != null">preg_name,</if>
            <if test="regId != null">reg_id,</if>
            <if test="regName != null">reg_name,</if>
            <if test="deviceName != null">device_name,</if>
            <if test="deviceType != null">device_type,</if>
            <if test="deviceAddress != null">device_address,</if>
            <if test="addTime != null">add_time,</if>
            <if test="lockNum != null">lock_num,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="system != null">#{system},</if>
            <if test="prvId != null">#{prvId},</if>
            <if test="prvName != null">#{prvName},</if>
            <if test="pregId != null">#{pregId},</if>
            <if test="pregName != null">#{pregName},</if>
            <if test="regId != null">#{regId},</if>
            <if test="regName != null">#{regName},</if>
            <if test="deviceName != null">#{deviceName},</if>
            <if test="deviceType != null">#{deviceType},</if>
            <if test="deviceAddress != null">#{deviceAddress},</if>
            <if test="addTime != null">#{addTime},</if>
            <if test="lockNum != null">#{lockNum},</if>
         </trim>
    </insert>
    <insert id="saveBatchIndexLockDevice">
        insert into index_lock_device(`system`, prv_id, prv_name, preg_id, preg_name, reg_id, reg_name, device_name, device_type, device_address, add_time, lock_num)
        values
            <foreach collection="list" item="item" separator=",">
                (#{item.system},#{item.prvId},#{item.prvName},#{item.pregId},#{item.pregName},#{item.regId},#{item.regName},#{item.deviceName},#{item.deviceType},#{item.deviceAddress},#{item.addTime},#{item.lockNum})
            </foreach>
    </insert>

    <update id="updateIndexLockDevice" parameterType="IndexLockDevice">
        update index_lock_device
        <trim prefix="SET" suffixOverrides=",">
            <if test="system != null">`system` = #{system},</if>
            <if test="prvId != null">prv_id = #{prvId},</if>
            <if test="prvName != null">prv_name = #{prvName},</if>
            <if test="pregId != null">preg_id = #{pregId},</if>
            <if test="pregName != null">preg_name = #{pregName},</if>
            <if test="regId != null">reg_id = #{regId},</if>
            <if test="regName != null">reg_name = #{regName},</if>
            <if test="deviceName != null">device_name = #{deviceName},</if>
            <if test="deviceType != null">device_type = #{deviceType},</if>
            <if test="deviceAddress != null">device_address = #{deviceAddress},</if>
            <if test="addTime != null">add_time = #{addTime},</if>
            <if test="lockNum != null">lock_num = #{lockNum},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteIndexLockDeviceById" parameterType="Long">
        delete from index_lock_device where id = #{id}
    </delete>

    <delete id="deleteIndexLockDeviceByIds" parameterType="String">
        delete from index_lock_device where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
