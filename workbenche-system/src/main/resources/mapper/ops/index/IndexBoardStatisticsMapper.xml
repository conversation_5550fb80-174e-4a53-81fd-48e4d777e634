<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzl.workbenche.ops.mapper.index.IndexBoardStatisticsMapper">

    <resultMap type="IndexBoardStatistics" id="IndexBoardStatisticsResult">
        <result property="id"    column="id"    />
        <result property="system"    column="system"    />
        <result property="prvId"    column="prv_id"    />
        <result property="prvName"    column="prv_name"    />
        <result property="pregId"    column="preg_id"    />
        <result property="pregName"    column="preg_name"    />
        <result property="regId"    column="reg_id"    />
        <result property="regName"    column="reg_name"    />
        <result property="opticalCableName"    column="optical_cable_name"    />
        <result property="opticalCableLevel"    column="optical_cable_level"    />
        <result property="fiberNum"    column="fiber_num"    />
        <result property="totalLength"    column="total_length"    />
        <result property="useFiberTotal"    column="use_fiber_total"    />
        <result property="highFiberTotal"    column="high_fiber_total"    />
        <result property="passFiberTotal"    column="pass_fiber_total"    />
        <result property="latLonDeviation"    column="lat_lon_deviation"    />
        <result property="repeatFiberTotal"    column="repeat_fiber_total"    />
        <result property="largeDecayUse"    column="large_decay_use"    />
        <result property="noUploadFiberTotal"    column="no_upload_fiber_total"    />
        <result property="largeDecayNoUse"    column="large_decay_no_use"    />
        <result property="segmentTotalFiber"    column="segment_total_fiber"    />
        <result property="portBreakTotal"    column="port_break_total"    />
        <result property="decayLocationNum"    column="decay_location_num"    />
        <result property="passDecayNum"    column="pass_decay_num"    />
        <result property="smallDecayNum"    column="small_decay_num"    />
        <result property="midDecayNum"    column="mid_decay_num"    />
        <result property="largeDecayNum"    column="large_decay_num"    />
        <result property="jointRepairNum"    column="joint_repair_num"    />
        <result property="replaceNum"    column="replace_num"    />
        <result property="time"    column="time"    />
    </resultMap>

    <sql id="selectIndexBoardStatisticsVo">
        select id, `system`, prv_id, prv_name, preg_id, preg_name, reg_id, reg_name, optical_cable_name, optical_cable_level, fiber_num, total_length, use_fiber_total, high_fiber_total, pass_fiber_total, lat_lon_deviation, repeat_fiber_total, large_decay_use, no_upload_fiber_total, large_decay_no_use, segment_total_fiber, port_break_total, decay_location_num, pass_decay_num, small_decay_num, mid_decay_num, large_decay_num, joint_repair_num, replace_num, time from index_board_statistics
    </sql>

    <select id="selectIndexBoardStatisticsList" parameterType="IndexBoardStatistics" resultMap="IndexBoardStatisticsResult">
        <include refid="selectIndexBoardStatisticsVo"/>
        <where>
            <if test="system != null  and system != ''"> and `system` = #{system}</if>
            <if test="prvId != null  and prvId != ''"> and prv_id = #{prvId}</if>
            <if test="prvName != null  and prvName != ''"> and prv_name like concat('%', #{prvName}, '%')</if>
            <if test="pregId != null  and pregId != ''"> and preg_id = #{pregId}</if>
            <if test="pregName != null  and pregName != ''"> and preg_name like concat('%', #{pregName}, '%')</if>
            <if test="regId != null  and regId != ''"> and reg_id = #{regId}</if>
            <if test="regName != null  and regName != ''"> and reg_name like concat('%', #{regName}, '%')</if>
            <if test="opticalCableName != null  and opticalCableName != ''"> and optical_cable_name like concat('%', #{opticalCableName}, '%')</if>
            <if test="opticalCableLevel != null  and opticalCableLevel != ''"> and optical_cable_level = #{opticalCableLevel}</if>
            <if test="fiberNum != null  and fiberNum != ''"> and fiber_num = #{fiberNum}</if>
            <if test="totalLength != null  and totalLength != ''"> and total_length = #{totalLength}</if>
            <if test="useFiberTotal != null  and useFiberTotal != ''"> and use_fiber_total = #{useFiberTotal}</if>
            <if test="highFiberTotal != null  and highFiberTotal != ''"> and high_fiber_total = #{highFiberTotal}</if>
            <if test="passFiberTotal != null  and passFiberTotal != ''"> and pass_fiber_total = #{passFiberTotal}</if>
            <if test="latLonDeviation != null  and latLonDeviation != ''"> and lat_lon_deviation = #{latLonDeviation}</if>
            <if test="repeatFiberTotal != null  and repeatFiberTotal != ''"> and repeat_fiber_total = #{repeatFiberTotal}</if>
            <if test="largeDecayUse != null  and largeDecayUse != ''"> and large_decay_use = #{largeDecayUse}</if>
            <if test="noUploadFiberTotal != null  and noUploadFiberTotal != ''"> and no_upload_fiber_total = #{noUploadFiberTotal}</if>
            <if test="largeDecayNoUse != null  and largeDecayNoUse != ''"> and large_decay_no_use = #{largeDecayNoUse}</if>
            <if test="segmentTotalFiber != null  and segmentTotalFiber != ''"> and segment_total_fiber = #{segmentTotalFiber}</if>
            <if test="portBreakTotal != null  and portBreakTotal != ''"> and port_break_total = #{portBreakTotal}</if>
            <if test="decayLocationNum != null  and decayLocationNum != ''"> and decay_location_num = #{decayLocationNum}</if>
            <if test="passDecayNum != null  and passDecayNum != ''"> and pass_decay_num = #{passDecayNum}</if>
            <if test="smallDecayNum != null  and smallDecayNum != ''"> and small_decay_num = #{smallDecayNum}</if>
            <if test="midDecayNum != null  and midDecayNum != ''"> and mid_decay_num = #{midDecayNum}</if>
            <if test="largeDecayNum != null  and largeDecayNum != ''"> and large_decay_num = #{largeDecayNum}</if>
            <if test="jointRepairNum != null  and jointRepairNum != ''"> and joint_repair_num = #{jointRepairNum}</if>
            <if test="replaceNum != null  and replaceNum != ''"> and replace_num = #{replaceNum}</if>
            <if test="time != null "> and time = #{time}</if>
        </where>
    </select>

    <select id="selectIndexBoardStatisticsById" parameterType="Long" resultMap="IndexBoardStatisticsResult">
        <include refid="selectIndexBoardStatisticsVo"/>
        where id = #{id}
    </select>
    <select id="countNum" resultType="java.lang.Long">
        select count(*) FROM index_board_statistics
    </select>
    <select id="statistics" resultType="java.util.Map">
        select sum(IFNULL(fiber_num,0)) as fiberNum,
               sum(IFNULL(joint_repair_num,0)) as jointRepairNum,
               sum(IFNULL(replace_num,0)) as replaceNum
        FROM index_board_statistics
    </select>
    <select id="cardStatistics" resultType="com.rzl.workbenche.ops.domain.index.vo.QualityAnalysisVo">
        SELECT sum(IFNULL(small_decay_num,0)) as smallDecayNumTotal,
               sum(IFNULL(mid_decay_num,0)) as midDecayNumTotal,
               sum(IFNULL(large_decay_num,0)) as largeDecayNumTotal,
               sum(IFNULL(pass_decay_num,0)) as passDecayNumTotal
        FROM index_board_statistics
    </select>
    <select id="fiberRateStatistics" resultType="com.rzl.workbenche.ops.domain.index.vo.FiberRateVo">
        SELECT preg_name as pregName,
               ROUND((sum(IFNULL(high_fiber_total,0)) +
                      sum(IFNULL(pass_fiber_total,0))) /
                     sum(IFNULL(fiber_num,0)),2) as value
        FROM index_board_statistics GROUP BY preg_name
    </select>
    <select id="fiberQuality" resultType="com.rzl.workbenche.ops.domain.index.vo.FiberQualityAnalysisVo">
        SELECT
            sum(IFNULL(high_fiber_total,0)) as highFiberTotalNum,
            sum(IFNULL(pass_fiber_total,0)) as passFiberTotalNum,
            sum(IFNULL(use_fiber_total,0)) as useFiberTotalNum,
            sum(IFNULL(segment_total_fiber,0)) as segmentFiberTotalNum
        FROM index_board_statistics
    </select>
    <select id="numStatistics" resultType="com.rzl.workbenche.ops.domain.index.vo.FiberRateVo">
        SELECT
            preg_name as pregName,
               count(*) as value
        FROM index_board_statistics GROUP BY preg_name
    </select>
    <select id="fiberLenth" resultType="com.rzl.workbenche.ops.domain.index.vo.FiberRateVo">
        SELECT
            temp.pregName,
               ROUND(SUM(temp.total/10000000),2) as value
        FROM
            (select  preg_name as pregName,ROUND(total_length * fiber_num,2) as total FROM index_board_statistics) temp
        GROUP BY temp.pregName
    </select>
    <select id="fiberSkinLenth" resultType="com.rzl.workbenche.ops.domain.index.vo.FiberRateVo">
        SELECT preg_name as pregName,
               ROUND(sum(total_length/1000),2) as value
        FROM index_board_statistics GROUP BY preg_name
    </select>
    <select id="selectPassRate" resultType="java.lang.Double">
        SELECT ROUND(sum(IFNULL(pass_fiber_total,0)) / sum(IFNULL(fiber_num,0)) * 100,2) as rate
        FROM `index_board_statistics`
    </select>

    <insert id="insertIndexBoardStatistics" parameterType="IndexBoardStatistics" useGeneratedKeys="true" keyProperty="id">
        insert into index_board_statistics
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="system != null">`system`,</if>
            <if test="prvId != null">prv_id,</if>
            <if test="prvName != null">prv_name,</if>
            <if test="pregId != null">preg_id,</if>
            <if test="pregName != null">preg_name,</if>
            <if test="regId != null">reg_id,</if>
            <if test="regName != null">reg_name,</if>
            <if test="opticalCableName != null">optical_cable_name,</if>
            <if test="opticalCableLevel != null">optical_cable_level,</if>
            <if test="fiberNum != null">fiber_num,</if>
            <if test="totalLength != null">total_length,</if>
            <if test="useFiberTotal != null">use_fiber_total,</if>
            <if test="highFiberTotal != null">high_fiber_total,</if>
            <if test="passFiberTotal != null">pass_fiber_total,</if>
            <if test="latLonDeviation != null">lat_lon_deviation,</if>
            <if test="repeatFiberTotal != null">repeat_fiber_total,</if>
            <if test="largeDecayUse != null">large_decay_use,</if>
            <if test="noUploadFiberTotal != null">no_upload_fiber_total,</if>
            <if test="largeDecayNoUse != null">large_decay_no_use,</if>
            <if test="segmentTotalFiber != null">segment_total_fiber,</if>
            <if test="portBreakTotal != null">port_break_total,</if>
            <if test="decayLocationNum != null">decay_location_num,</if>
            <if test="passDecayNum != null">pass_decay_num,</if>
            <if test="smallDecayNum != null">small_decay_num,</if>
            <if test="midDecayNum != null">mid_decay_num,</if>
            <if test="largeDecayNum != null">large_decay_num,</if>
            <if test="jointRepairNum != null">joint_repair_num,</if>
            <if test="replaceNum != null">replace_num,</if>
            <if test="time != null">time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="system != null">#{system},</if>
            <if test="prvId != null">#{prvId},</if>
            <if test="prvName != null">#{prvName},</if>
            <if test="pregId != null">#{pregId},</if>
            <if test="pregName != null">#{pregName},</if>
            <if test="regId != null">#{regId},</if>
            <if test="regName != null">#{regName},</if>
            <if test="opticalCableName != null">#{opticalCableName},</if>
            <if test="opticalCableLevel != null">#{opticalCableLevel},</if>
            <if test="fiberNum != null">#{fiberNum},</if>
            <if test="totalLength != null">#{totalLength},</if>
            <if test="useFiberTotal != null">#{useFiberTotal},</if>
            <if test="highFiberTotal != null">#{highFiberTotal},</if>
            <if test="passFiberTotal != null">#{passFiberTotal},</if>
            <if test="latLonDeviation != null">#{latLonDeviation},</if>
            <if test="repeatFiberTotal != null">#{repeatFiberTotal},</if>
            <if test="largeDecayUse != null">#{largeDecayUse},</if>
            <if test="noUploadFiberTotal != null">#{noUploadFiberTotal},</if>
            <if test="largeDecayNoUse != null">#{largeDecayNoUse},</if>
            <if test="segmentTotalFiber != null">#{segmentTotalFiber},</if>
            <if test="portBreakTotal != null">#{portBreakTotal},</if>
            <if test="decayLocationNum != null">#{decayLocationNum},</if>
            <if test="passDecayNum != null">#{passDecayNum},</if>
            <if test="smallDecayNum != null">#{smallDecayNum},</if>
            <if test="midDecayNum != null">#{midDecayNum},</if>
            <if test="largeDecayNum != null">#{largeDecayNum},</if>
            <if test="jointRepairNum != null">#{jointRepairNum},</if>
            <if test="replaceNum != null">#{replaceNum},</if>
            <if test="time != null">#{time},</if>
         </trim>
    </insert>
    <insert id="saveBatchBoardStatistics">
        insert into index_board_statistics(`system`, prv_id, prv_name, preg_id, preg_name, reg_id, reg_name, optical_cable_name, optical_cable_level, fiber_num, total_length, use_fiber_total, high_fiber_total, pass_fiber_total, lat_lon_deviation, repeat_fiber_total, large_decay_use, no_upload_fiber_total, large_decay_no_use, segment_total_fiber, port_break_total, decay_location_num, pass_decay_num, small_decay_num, mid_decay_num, large_decay_num, joint_repair_num, replace_num, time)
        values
            <foreach collection="list" item="item" separator=",">
                (#{item.system},#{item.prvId},#{item.prvName},#{item.pregId},#{item.pregName},#{item.regId},#{item.regName},#{item.opticalCableName},#{item.opticalCableLevel},#{item.fiberNum},#{item.totalLength},#{item.useFiberTotal},#{item.highFiberTotal},#{item.passFiberTotal},#{item.latLonDeviation},#{item.repeatFiberTotal},#{item.largeDecayUse},#{item.noUploadFiberTotal},#{item.largeDecayNoUse},#{item.segmentTotalFiber},#{item.portBreakTotal},#{item.decayLocationNum},#{item.passDecayNum},#{item.smallDecayNum},#{item.midDecayNum},#{item.largeDecayNum},#{item.jointRepairNum},#{item.replaceNum},#{item.time})
            </foreach>
    </insert>

    <update id="updateIndexBoardStatistics" parameterType="IndexBoardStatistics">
        update index_board_statistics
        <trim prefix="SET" suffixOverrides=",">
            <if test="system != null">`system` = #{system},</if>
            <if test="prvId != null">prv_id = #{prvId},</if>
            <if test="prvName != null">prv_name = #{prvName},</if>
            <if test="pregId != null">preg_id = #{pregId},</if>
            <if test="pregName != null">preg_name = #{pregName},</if>
            <if test="regId != null">reg_id = #{regId},</if>
            <if test="regName != null">reg_name = #{regName},</if>
            <if test="opticalCableName != null">optical_cable_name = #{opticalCableName},</if>
            <if test="opticalCableLevel != null">optical_cable_level = #{opticalCableLevel},</if>
            <if test="fiberNum != null">fiber_num = #{fiberNum},</if>
            <if test="totalLength != null">total_length = #{totalLength},</if>
            <if test="useFiberTotal != null">use_fiber_total = #{useFiberTotal},</if>
            <if test="highFiberTotal != null">high_fiber_total = #{highFiberTotal},</if>
            <if test="passFiberTotal != null">pass_fiber_total = #{passFiberTotal},</if>
            <if test="latLonDeviation != null">lat_lon_deviation = #{latLonDeviation},</if>
            <if test="repeatFiberTotal != null">repeat_fiber_total = #{repeatFiberTotal},</if>
            <if test="largeDecayUse != null">large_decay_use = #{largeDecayUse},</if>
            <if test="noUploadFiberTotal != null">no_upload_fiber_total = #{noUploadFiberTotal},</if>
            <if test="largeDecayNoUse != null">large_decay_no_use = #{largeDecayNoUse},</if>
            <if test="segmentTotalFiber != null">segment_total_fiber = #{segmentTotalFiber},</if>
            <if test="portBreakTotal != null">port_break_total = #{portBreakTotal},</if>
            <if test="decayLocationNum != null">decay_location_num = #{decayLocationNum},</if>
            <if test="passDecayNum != null">pass_decay_num = #{passDecayNum},</if>
            <if test="smallDecayNum != null">small_decay_num = #{smallDecayNum},</if>
            <if test="midDecayNum != null">mid_decay_num = #{midDecayNum},</if>
            <if test="largeDecayNum != null">large_decay_num = #{largeDecayNum},</if>
            <if test="jointRepairNum != null">joint_repair_num = #{jointRepairNum},</if>
            <if test="replaceNum != null">replace_num = #{replaceNum},</if>
            <if test="time != null">time = #{time},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteIndexBoardStatisticsById" parameterType="Long">
        delete from index_board_statistics where id = #{id}
    </delete>

    <delete id="deleteIndexBoardStatisticsByIds" parameterType="String">
        delete from index_board_statistics where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
