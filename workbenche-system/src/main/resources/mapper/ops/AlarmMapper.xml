<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzl.workbenche.ops.mapper.AlarmMapper">

    <resultMap type="Alarm" id="AlarmResult">
        <result property="id"    column="id"    />
        <result property="alarmCode"    column="alarm_code"    />
        <result property="alarmTitle"    column="alarm_title"    />
        <result property="prvId"    column="prv_id"    />
        <result property="prvName"    column="prv_name"    />
        <result property="pregId"    column="preg_id"    />
        <result property="pregName"    column="preg_name"    />
        <result property="regId"    column="reg_id"    />
        <result property="regName"    column="reg_name"    />
        <result property="alarmContent"    column="alarm_content"    />
        <result property="occurTime"    column="occur_time"    />
        <result property="cleanTime"    column="clean_time"    />
        <result property="handleStatus"    column="handle_status"    />
        <result property="grade"    column="grade"    />
        <result property="alarmUser"    column="alarm_user"    />
        <result property="userId"    column="user_id"    />
        <result property="alarmType"    column="alarm_type"    />
        <result property="system"    column="system"    />
        <result property="duration"    column="duration"    />
        <result property="opinion"    column="opinion"    />
        <result property="handleTime"    column="handle_time"    />
        <result property="handleUser"    column="handle_user"    />
        <result property="alarmName"    column="alarmName"    />
        <result property="systemName"    column="systemName"    />
    </resultMap>
    <resultMap id="AlarmAlarmResult" type="AlarmAnalysis">
        <result property="system" column="system" />
        <result property="total" column="total" />
        <result property="doneTotal" column="doneTotal" />
        <result property="duration" column="duration" />
        <result property="month" column="month" />
    </resultMap>

    <sql id="selectAlarmVo">
        select id, alarm_code, alarm_title, prv_id, prv_name, preg_id, preg_name, reg_id, reg_name, alarm_content, occur_time, clean_time, handle_status, grade, alarm_user, user_id, alarm_type, `system`, duration, opinion, handle_time, handle_user from alarm
    </sql>

    <select id="selectAlarmList" parameterType="Alarm" resultMap="AlarmResult">
        select a.id, a.alarm_code, a.alarm_title, a.prv_id, a.prv_name,
        a.preg_id, a.preg_name, a.reg_id, a.reg_name, a.alarm_content,
        a.occur_time, a.clean_time, a.handle_status, a.grade, a.alarm_user,
        a.user_id, a.alarm_type, a.`system`, a.duration, a.opinion, a.handle_time, a.handle_user,sa.business_name alarmName,
        bd.system_name systemName from alarm a
        LEFT JOIN sys_alarm sa
        on a.`system` = sa.`system` and a.alarm_type = sa.business_value
        LEFT JOIN business_data_config bd on  bd.system_code = a.`system`
        <where>
            <if test="alarmTitle != null  and alarmTitle != ''"> and (a.alarm_title like concat('%',#{alarmTitle},'%') or a.alarm_code like concat('%',#{alarmTitle},'%'))</if>
            <if test="prvId != null  and prvId != ''"> and a.prv_id = #{prvId}</if>
            <if test="prvName != null  and prvName != ''"> and a.prv_name like concat('%', #{prvName}, '%')</if>
            <if test="pregId != null  and pregId != ''"> and a.preg_id = #{pregId}</if>
            <if test="pregName != null  and pregName != ''"> and a.preg_name like concat('%', #{pregName}, '%')</if>
            <if test="regId != null  and regId != ''"> and a.reg_id = #{regId}</if>
            <if test="regName != null  and regName != ''"> and a.reg_name like concat('%', #{regName}, '%')</if>
            <if test="handleStatus != null  and handleStatus != ''"> and a.handle_status = #{handleStatus}</if>
            <if test="grade != null  and grade != ''"> and a.grade = #{grade}</if>
            <if test="alarmUser != null  and alarmUser != ''"> and a.alarm_user = #{alarmUser}</if>
            <if test="userId != null "> and a.user_id = #{userId}</if>
            <if test="alarmType != null  and alarmType != ''"> and a.alarm_type = #{alarmType}</if>
            <if test="system != null  and system != ''"> and a.`system` = #{system}</if>
            <if test="regIds.size() > 0 and regIds != null">
                and a.reg_id in
                <foreach collection="regIds" item="regId" separator="," close=")" open="(">
                    #{regId}
                </foreach>
            </if>
        </where>
    order by a.occur_time desc
    </select>

    <select id="selectAlarmById" parameterType="Long" resultMap="AlarmResult">
        <include refid="selectAlarmVo"/>
        where id = #{id}
    </select>
    <select id="count" resultType="java.lang.Long">
        select count(*) from alarm
        <where>
            <if test="handleStatus != null">
                handle_status = #{handleStatus}
            </if>
        </where>
    </select>
    <select id="selectByAlarmCode" resultMap="AlarmResult">
        <include refid="selectAlarmVo"/>
        where alarm_code = #{alarmCode} and `system` = #{system}
    </select>
    <select id="selectSystem" resultType="java.lang.String">
        select distinct `system` from alarm
    </select>
    <select id="selectAlarmAnalysis" resultMap="AlarmAlarmResult">
        SELECT `system`,DATE_FORMAT(occur_time, '%Y-%m') AS month, COUNT(*) AS total ,
        SUM(CASE WHEN handle_status = '1' THEN 1 ELSE 0 END) AS doneTotal,
        SUM(IFNULL(duration,0)) as duration
        FROM alarm
        WHERE `system` = #{system}
        GROUP BY month
        ORDER BY month desc
    </select>

    <insert id="insertAlarm" parameterType="Alarm" useGeneratedKeys="true" keyProperty="id">
        insert into alarm
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="alarmCode != null">alarm_code,</if>
            <if test="alarmTitle != null">alarm_title,</if>
            <if test="prvId != null">prv_id,</if>
            <if test="prvName != null">prv_name,</if>
            <if test="pregId != null">preg_id,</if>
            <if test="pregName != null">preg_name,</if>
            <if test="regId != null">reg_id,</if>
            <if test="regName != null">reg_name,</if>
            <if test="alarmContent != null">alarm_content,</if>
            <if test="occurTime != null">occur_time,</if>
            <if test="cleanTime != null">clean_time,</if>
            <if test="handleStatus != null">handle_status,</if>
            <if test="grade != null">grade,</if>
            <if test="alarmUser != null">alarm_user,</if>
            <if test="userId != null">user_id,</if>
            <if test="alarmType != null">alarm_type,</if>
            <if test="system != null">`system`,</if>
            <if test="duration != null">duration,</if>
            <if test="opinion != null">opinion,</if>
            <if test="handleTime != null">handle_time,</if>
            <if test="handleUser != null">handle_user,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="alarmCode != null">#{alarmCode},</if>
            <if test="alarmTitle != null">#{alarmTitle},</if>
            <if test="prvId != null">#{prvId},</if>
            <if test="prvName != null">#{prvName},</if>
            <if test="pregId != null">#{pregId},</if>
            <if test="pregName != null">#{pregName},</if>
            <if test="regId != null">#{regId},</if>
            <if test="regName != null">#{regName},</if>
            <if test="alarmContent != null">#{alarmContent},</if>
            <if test="occurTime != null">#{occurTime},</if>
            <if test="cleanTime != null">#{cleanTime},</if>
            <if test="handleStatus != null">#{handleStatus},</if>
            <if test="grade != null">#{grade},</if>
            <if test="alarmUser != null">#{alarmUser},</if>
            <if test="userId != null">#{userId},</if>
            <if test="alarmType != null">#{alarmType},</if>
            <if test="system != null">#{system},</if>
            <if test="duration != null">#{duration},</if>
            <if test="opinion != null">#{opinion},</if>
            <if test="handleTime != null">#{handleTime},</if>
            <if test="handleUser != null">#{handleUser},</if>
        </trim>
    </insert>
    <insert id="saveBatchAlarm">
        insert into alarm(alarm_code, alarm_title, prv_id, prv_name, preg_id, preg_name, reg_id, reg_name, alarm_content, occur_time, clean_time, handle_status, grade, alarm_user, user_id, alarm_type, `system`, duration, opinion, handle_time, handle_user)
        values
            <foreach collection="list" separator="," item="item">
                (#{item.alarmCode},#{item.alarmTitle},#{item.prvId},#{item.prvName},#{item.pregId},#{item.pregName},#{item.regId},#{item.regName},#{item.alarmContent},#{item.occurTime},#{item.cleanTime},#{item.handleStatus},#{item.grade},#{item.alarmUser},#{item.userId},#{item.alarmType},#{item.system},#{item.duration},#{item.opinion},#{item.handleTime},#{item.handleUser})
            </foreach>
    </insert>

    <update id="updateByCode" parameterType="Alarm">
        update alarm
        <trim prefix="SET" suffixOverrides=",">
            <if test="handleStatus != null">handle_status = #{handleStatus},</if>
            <if test="opinion != null">opinion = #{opinion},</if>
            <if test="handleTime != null">handle_time = #{handleTime},</if>
            <if test="handleUser != null">handle_user = #{handleUser},</if>
            <if test="duration != null">duration = #{duration},</if>
        </trim>
        where alarm_code = #{alarmCode} and system = #{system}
    </update>
    <update id="updateAlarm" parameterType="Alarm">
        update alarm
        <trim prefix="SET" suffixOverrides=",">
            <if test="alarmCode != null">alarm_code = #{alarmCode},</if>
            <if test="alarmTitle != null">alarm_title = #{alarmTitle},</if>
            <if test="prvId != null">prv_id = #{prvId},</if>
            <if test="prvName != null">prv_name = #{prvName},</if>
            <if test="pregId != null">preg_id = #{pregId},</if>
            <if test="pregName != null">preg_name = #{pregName},</if>
            <if test="regId != null">reg_id = #{regId},</if>
            <if test="regName != null">reg_name = #{regName},</if>
            <if test="alarmContent != null">alarm_content = #{alarmContent},</if>
            <if test="occurTime != null">occur_time = #{occurTime},</if>
            <if test="cleanTime != null">clean_time = #{cleanTime},</if>
            <if test="handleStatus != null">handle_status = #{handleStatus},</if>
            <if test="grade != null">grade = #{grade},</if>
            <if test="alarmUser != null">alarm_user = #{alarmUser},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="alarmType != null">alarm_type = #{alarmType},</if>
            <if test="system != null">`system` = #{system},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="opinion != null">opinion = #{opinion},</if>
            <if test="handleTime != null">handle_time = #{handleTime},</if>
            <if test="handleUser != null">handle_user = #{handleUser},</if>
        </trim>
        where id = #{id}
    </update>
    <delete id="deleteAlarmById" parameterType="Long">
        delete from alarm where id = #{id}
    </delete>

    <delete id="deleteAlarmByIds" parameterType="String">
        delete from alarm where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
