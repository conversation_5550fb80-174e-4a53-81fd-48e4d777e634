<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzl.workbenche.ops.mapper.OpsElectricityReportMapper">

    <resultMap type="OpsElectricityReport" id="OpsElectricityReportResult">
        <result property="id"    column="id"    />
        <result property="mac"    column="mac"    />
        <result property="macName"    column="mac_name"    />
        <result property="stationName"    column="station_name"    />
        <result property="stationNo"    column="station_no"    />
        <result property="stationType"    column="station_type"    />
        <result property="operatorName"    column="operator_name"    />
        <result property="addressName"    column="address_name"    />
        <result property="areaName"    column="area_name"    />
        <result property="totalNum"    column="total_num"    />
        <result property="totalFee"    column="total_fee"    />
        <result property="thriftNum"    column="thrift_num"    />
        <result property="thriftTime"    column="thrift_time"    />
        <result property="thrifFee"    column="thrif_fee"    />
        <result property="statisticsTime"    column="statistics_time"    />
    </resultMap>

    <sql id="selectOpsElectricityReportVo">
        select id, mac, mac_name, station_name, station_no, station_type, operator_name, address_name, area_name, total_num, total_fee, thrift_num, thrift_time, thrif_fee, statistics_time from ops_electricity_report
    </sql>

    <select id="selectOpsElectricityReportList" parameterType="OpsElectricityReport" resultMap="OpsElectricityReportResult">
        <include refid="selectOpsElectricityReportVo"/>
        <where>
            <if test="mac != null  and mac != ''"> and mac like concat('%', #{mac}, '%')</if>
            <if test="macName != null  and macName != ''"> and mac_name like concat('%', #{macName}, '%')</if>
            <if test="stationName != null  and stationName != ''"> and station_name like concat('%', #{stationName}, '%')</if>
        </where>
    </select>

</mapper>
