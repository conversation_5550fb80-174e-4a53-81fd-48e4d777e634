<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzl.workbenche.ops.mapper.OpsSystemGuideMapper">

    <resultMap type="OpsSystemGuide" id="OpsSystemGuideResult">
        <result property="id"    column="id"    />
        <result property="systemCode"    column="system_code"    />
        <result property="systemName"    column="systemName"    />
        <result property="systemMark"    column="system_mark"    />
        <result property="createTime"    column="create_time"    />
        <result property="loginUrl"    column="login_url"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="selectOpsSystemGuideVo">
        select sg.id, sg.system_code, bc.system_name systemName, sg.system_mark, sg.create_time, sg.login_url, sg.status from ops_system_guide sg
        left join business_data_config bc on bc.system_code = sg.system_code
    </sql>

    <select id="selectOpsSystemGuideList" parameterType="OpsSystemGuide" resultMap="OpsSystemGuideResult">
        <include refid="selectOpsSystemGuideVo"/>
        <where>
            <if test="systemCode != null  and systemCode != ''"> and sg.system_code = #{systemCode}</if>
            <if test="systemName != null  and systemName != ''"> and sg.system_name like concat('%', #{systemName}, '%')</if>
        </where>
    </select>

    <select id="selectOpsSystemGuideById" parameterType="Long" resultMap="OpsSystemGuideResult">
        <include refid="selectOpsSystemGuideVo"/>
        where sg.id = #{id}
    </select>
    <select id="selectBySystemCode" resultMap="OpsSystemGuideResult">
        <include refid="selectOpsSystemGuideVo"/>
        where sg.system_code = #{systemCode}
    </select>
    <select id="selectBySystemMark" resultMap="OpsSystemGuideResult">
        <include refid="selectOpsSystemGuideVo"/>
        where sg.system_mark = #{systemMark}
    </select>

    <insert id="insertOpsSystemGuide" parameterType="OpsSystemGuide" useGeneratedKeys="true" keyProperty="id">
        insert into ops_system_guide
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="systemCode != null">system_code,</if>
            <if test="systemName != null">system_name,</if>
            <if test="systemMark != null">system_mark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="loginUrl != null">login_url,</if>
            <if test="status != null">status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="systemCode != null">#{systemCode},</if>
            <if test="systemName != null">#{systemName},</if>
            <if test="systemMark != null">#{systemMark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="loginUrl != null">#{loginUrl},</if>
            <if test="status != null">#{status},</if>
        </trim>
    </insert>

    <update id="updateOpsSystemGuide" parameterType="OpsSystemGuide">
        update ops_system_guide
        <trim prefix="SET" suffixOverrides=",">
            <if test="systemMark != null and systemCode != ''">system_mark = #{systemMark},</if>
            <if test="loginUrl != null and systemCode != ''">login_url = #{loginUrl},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOpsSystemGuideById" parameterType="Long">
        delete from ops_system_guide where id = #{id}
    </delete>

    <delete id="deleteOpsSystemGuideByIds" parameterType="String">
        delete from ops_system_guide where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>