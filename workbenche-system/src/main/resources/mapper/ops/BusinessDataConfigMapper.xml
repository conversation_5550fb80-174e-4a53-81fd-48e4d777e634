<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzl.workbenche.ops.mapper.BusinessDataConfigMapper">

    <resultMap type="BusinessDataConfig" id="BusinessDataConfigResult">
        <result property="id"    column="id"    />
        <result property="systemCode"    column="system_code"    />
        <result property="systemName"    column="system_name"    />
        <result property="sysPub"    column="sys_pub"    />
        <result property="systemUrl"    column="system_url"    />
        <result property="createTime"    column="create_time"    />
        <result property="opsPub"    column="ops_pub"    />
    </resultMap>

    <sql id="selectBusinessDataConfigVo">
        select id, system_code, system_name, sys_pub, system_url, create_time,ops_pub from business_data_config
    </sql>

    <select id="selectBusinessDataConfigList" parameterType="BusinessDataConfig" resultMap="BusinessDataConfigResult">
        <include refid="selectBusinessDataConfigVo"/>
        <where>
            <if test="systemCode != null  and systemCode != ''"> and system_code = #{systemCode}</if>
            <if test="systemName != null  and systemName != ''"> and system_name like concat('%', #{systemName}, '%')</if>
        </where>
    </select>

    <select id="selectBusinessDataConfigById" parameterType="Long" resultMap="BusinessDataConfigResult">
        <include refid="selectBusinessDataConfigVo"/>
        where id = #{id}
    </select>
    <select id="selectBySystem" resultMap="BusinessDataConfigResult">
        <include refid="selectBusinessDataConfigVo"/>
        where system_code = #{system}
    </select>

    <insert id="insertBusinessDataConfig" parameterType="BusinessDataConfig">
        insert into business_data_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="systemCode != null">system_code,</if>
            <if test="systemName != null">system_name,</if>
            <if test="sysPub != null">sys_pub,</if>
            <if test="systemUrl != null">system_url,</if>
            <if test="createTime != null">create_time,</if>
            <if test="opsPub != null">ops_pub,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="systemCode != null">#{systemCode},</if>
            <if test="systemName != null">#{systemName},</if>
            <if test="sysPub != null">#{sysPub},</if>
            <if test="systemUrl != null">#{systemUrl},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="opsPub != null">#{opsPub},</if>
        </trim>
    </insert>

    <update id="updateBusinessDataConfig" parameterType="BusinessDataConfig">
        update business_data_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="systemCode != null">system_code = #{systemCode},</if>
            <if test="systemName != null">system_name = #{systemName},</if>
            <if test="sysPub != null">sys_pub = #{sysPub},</if>
            <if test="systemUrl != null">system_url = #{systemUrl},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBusinessDataConfigById" parameterType="Long">
        delete from business_data_config where id = #{id}
    </delete>

    <delete id="deleteBusinessDataConfigByIds" parameterType="String">
        delete from business_data_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>