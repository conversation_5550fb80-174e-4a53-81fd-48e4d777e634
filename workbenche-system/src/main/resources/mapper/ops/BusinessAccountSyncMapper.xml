<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rzl.workbenche.ops.mapper.BusinessAccountSyncMapper">

    <resultMap type="BusinessAccountSync" id="BusinessAccountSyncResult">
        <result property="id"    column="id"    />
        <result property="system"    column="system"    />
        <result property="username"    column="username"    />
        <result property="phone"    column="phone"    />
        <result property="status"    column="status"    />
        <result property="syncTime"    column="sync_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateUser"    column="update_user"    />
    </resultMap>

    <sql id="selectBusinessAccountSyncVo">
        select id, `system`, username, phone, status, sync_time, update_time, update_user from business_account_sync
    </sql>

    <select id="selectBusinessAccountSyncList" parameterType="BusinessAccountSync" resultMap="BusinessAccountSyncResult">
        <include refid="selectBusinessAccountSyncVo"/>
        <where>
            <if test="system != null  and system != ''"> and `system` = #{system}</if>
            <if test="username != null  and username != ''"> and username like concat('%', #{username}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectBusinessAccountSyncById" parameterType="Long" resultMap="BusinessAccountSyncResult">
        <include refid="selectBusinessAccountSyncVo"/>
        where id = #{id}
    </select>
    <select id="selectByUsernameAndSystem"  resultMap="BusinessAccountSyncResult">
        <include refid="selectBusinessAccountSyncVo"/>
        where `system` = #{system} and username = #{username}
    </select>

    <insert id="insertBusinessAccountSync" parameterType="BusinessAccountSync" useGeneratedKeys="true" keyProperty="id">
        insert into business_account_sync
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="system != null">`system`,</if>
            <if test="username != null">username,</if>
            <if test="phone != null">phone,</if>
            <if test="status != null">status,</if>
            <if test="syncTime != null">sync_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateUser != null">update_user,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="system != null">#{system},</if>
            <if test="username != null">#{username},</if>
            <if test="phone != null">#{phone},</if>
            <if test="status != null">#{status},</if>
            <if test="syncTime != null">#{syncTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateUser != null">#{updateUser},</if>
        </trim>
    </insert>
    <insert id="saveBatch" parameterType="AccountSyncVo">
        insert into business_account_sync(`system`,username,phone,status,sync_time,update_time,update_user)
        values
            <foreach collection="list" item="item" separator=",">
                (#{item.system},#{item.username},#{item.phone},#{item.status},#{item.syncTime},#{item.updateTime},#{item.updateUser})
            </foreach>
    </insert>

    <update id="updateBusinessAccountSync" parameterType="BusinessAccountSync">
        update business_account_sync
        <trim prefix="SET" suffixOverrides=",">
            <if test="system != null">`system` = #{system},</if>
            <if test="username != null">username = #{username},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="status != null">status = #{status},</if>
            <if test="syncTime != null">sync_time = #{syncTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBusinessAccountSyncById" parameterType="Long">
        delete from business_account_sync where id = #{id}
    </delete>

    <delete id="deleteBusinessAccountSyncByIds" parameterType="String">
        delete from business_account_sync where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteByUsernames">
        delete from business_account_sync where `system` = #{system}
        and username in
        <foreach collection="usernames" item="username" separator="," close=")" open="(">
            #{username}
        </foreach>
    </delete>
</mapper>
