package com.rzl.workbenche.system.domain;

import com.rzl.workbenche.common.annotation.Excel;
import com.rzl.workbenche.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 用户和指标关系对象 sys_user_index
 *
 * <AUTHOR>
 * @date 2023-09-07
 */
public class SysUserIndex extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    /** 指示id */
    @Excel(name = "指示id")
    private Long indexId;

    /** 指标类型 ：1 卡片 2：图标 */
    @Excel(name = "指标类型 ：1 卡片 2：图标")
    private String type;

    /** 指标排序 */
    @Excel(name = "指标排序")
    private Long indexOrder;

    /** 业务系统 */
    @Excel(name = "业务系统")
    private String system;

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }
    public void setIndexId(Long indexId)
    {
        this.indexId = indexId;
    }

    public Long getIndexId()
    {
        return indexId;
    }
    public void setType(String type)
    {
        this.type = type;
    }

    public String getType()
    {
        return type;
    }
    public void setIndexOrder(Long indexOrder)
    {
        this.indexOrder = indexOrder;
    }

    public Long getIndexOrder()
    {
        return indexOrder;
    }
    public void setSystem(String system)
    {
        this.system = system;
    }

    public String getSystem()
    {
        return system;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("userId", getUserId())
                .append("indexId", getIndexId())
                .append("type", getType())
                .append("indexOrder", getIndexOrder())
                .append("system", getSystem())
                .toString();
    }
}