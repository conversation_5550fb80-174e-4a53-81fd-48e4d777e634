package com.rzl.workbenche.system.mapper;

import com.rzl.workbenche.system.domain.SysAlarm;
import com.rzl.workbenche.system.domain.vo.SysAlarmVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SysAlarmMapper {
    /**
     * 通过系统差对应告警类型
     * @param system
     * @return
     */
    List<SysAlarm> selectBySys(@Param("system") String system,@Param("businessType") String businessType);
    /**
     * 根据系统及类型查询名称
     */
    String selectAlarmName(@Param("system") String system,@Param("alarmType") String alarmType);

    /**
     * 查询系统与告警类型关系
     *
     * @param id 系统与告警类型关系主键
     * @return 系统与告警类型关系
     */
    public SysAlarm selectSysAlarmById(Long id);

    /**
     * 查询系统与告警类型关系列表
     *
     * @param sysAlarm 系统与告警类型关系
     * @return 系统与告警类型关系集合
     */
    public List<SysAlarmVo> selectSysAlarmList(SysAlarm sysAlarm);

    /**
     * 新增系统与告警类型关系
     *
     * @param sysAlarm 系统与告警类型关系
     * @return 结果
     */
    public int insertSysAlarm(SysAlarm sysAlarm);

    /**
     * 修改系统与告警类型关系
     *
     * @param sysAlarm 系统与告警类型关系
     * @return 结果
     */
    public int updateSysAlarm(SysAlarm sysAlarm);

    /**
     * 删除系统与告警类型关系
     *
     * @param id 系统与告警类型关系主键
     * @return 结果
     */
    public int deleteSysAlarmById(Long id);

    void insertBatchSysAlarm(List<SysAlarm> sysAlarms);
}
