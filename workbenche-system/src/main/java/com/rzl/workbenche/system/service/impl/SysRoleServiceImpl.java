package com.rzl.workbenche.system.service.impl;

import java.util.*;
import com.rzl.workbenche.common.constant.UserConstants;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.core.domain.entity.SysRole;
import com.rzl.workbenche.common.core.domain.entity.SysUser;
import com.rzl.workbenche.common.exception.ServiceException;
import com.rzl.workbenche.common.utils.SecurityUtils;
import com.rzl.workbenche.common.utils.StrUtils;
import com.rzl.workbenche.common.utils.spring.SpringUtils;
import com.rzl.workbenche.system.domain.SysRoleIndex;
import com.rzl.workbenche.system.domain.SysRoleMenu;
import com.rzl.workbenche.system.domain.SysRoleRegion;
import com.rzl.workbenche.system.domain.SysUserRole;
import com.rzl.workbenche.system.mapper.*;
import com.rzl.workbenche.system.service.ISysRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 角色 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysRoleServiceImpl implements ISysRoleService
{
    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SysRoleMenuMapper roleMenuMapper;

    @Autowired
    private SysUserRoleMapper userRoleMapper;

    @Autowired
    private SysRoleRegionMapper roleRegionMapper;
    @Autowired
    private SysRoleIndexMapper sysRoleIndexMapper;

    /**
     * 根据条件分页查询角色数据
     *
     * @param role 角色信息
     * @return 角色数据集合信息
     */
    @Override
//    @DataScope(regionAlias = "r")
    public List<SysRole> selectRoleList(SysRole role)
    {
        return roleMapper.selectRoleList(role);
    }

    /**
     * 根据用户ID查询角色
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    @Override
    public List<SysRole> selectRolesByUserId(Long userId)
    {
        List<SysRole> userRoles = roleMapper.selectRolePermissionByUserId(userId);
        List<SysRole> roles = selectRoleAll();
        for (SysRole role : roles)
        {
            for (SysRole userRole : userRoles)
            {
                if (role.getRoleId().longValue() == userRole.getRoleId().longValue())
                {
                    role.setFlag(true);
                    break;
                }
            }
        }
        return roles;
    }

    /**
     * 根据用户ID查询权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    @Override
    public Set<String> selectRolePermissionByUserId(Long userId)
    {
        List<SysRole> perms = roleMapper.selectRolePermissionByUserId(userId);
        Set<String> permsSet = new HashSet<>();
        for (SysRole perm : perms)
        {
            if (StrUtils.isNotNull(perm))
            {
                permsSet.addAll(Arrays.asList(perm.getRoleKey().trim().split(",")));
            }
        }
        return permsSet;
    }

    /**
     * 查询所有角色
     *
     * @return 角色列表
     */
    @Override
    public List<SysRole> selectRoleAll()
    {
        return SpringUtils.getAopProxy(this).selectRoleList(new SysRole());
    }

    /**
     * 根据用户ID获取角色选择框列表
     *
     * @param userId 用户ID
     * @return 选中角色ID列表
     */
    @Override
    public List<Long> selectRoleListByUserId(Long userId)
    {
        return roleMapper.selectRoleListByUserId(userId);
    }

    /**
     * 通过角色ID查询角色
     *
     * @param roleId 角色ID
     * @return 角色对象信息
     */
    @Override
    public SysRole selectRoleById(Long roleId)
    {
        return roleMapper.selectRoleById(roleId);
    }

    /**
     * 校验角色名称是否唯一
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    public String checkRoleNameUnique(SysRole role)
    {
        Long roleId = StrUtils.isNull(role.getRoleId()) ? -1L : role.getRoleId();
        SysRole info = roleMapper.checkRoleNameUnique(role.getRoleName());
        if (StrUtils.isNotNull(info) && info.getRoleId().longValue() != roleId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验角色权限是否唯一
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    public String checkRoleKeyUnique(SysRole role)
    {
        Long roleId = StrUtils.isNull(role.getRoleId()) ? -1L : role.getRoleId();
        SysRole info = roleMapper.checkRoleKeyUnique(role.getRoleKey());
        if (StrUtils.isNotNull(info) && info.getRoleId().longValue() != roleId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验角色是否允许操作
     *
     * @param role 角色信息
     */
    @Override
    public void checkRoleAllowed(SysRole role)
    {
        if (StrUtils.isNotNull(role.getRoleId()) && role.isAdmin())
        {
            throw new ServiceException("不允许操作超级管理员角色");
        }
    }

    /**
     * 校验角色是否有数据权限
     *
     * @param roleId 角色id
     */
    @Override
    public void checkRoleDataScope(Long roleId)
    {
        if (!SysUser.isAdmin(SecurityUtils.getUserId()))
        {
            SysRole role = new SysRole();
            role.setRoleId(roleId);
            List<SysRole> roles = SpringUtils.getAopProxy(this).selectRoleList(role);
            if (StrUtils.isEmpty(roles))
            {
                throw new ServiceException("没有权限访问角色数据！");
            }
        }
    }

    /**
     * 通过角色ID查询角色使用数量
     *
     * @param roleId 角色ID
     * @return 结果
     */
    @Override
    public int countUserRoleByRoleId(Long roleId)
    {
        return userRoleMapper.countUserRoleByRoleId(roleId);
    }

    /**
     * 新增保存角色信息
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertRole(SysRole role)
    {
        // 新增角色信息
        roleMapper.insertRole(role);
        return insertRoleMenu(role);
    }
    /**
     * 向导新增角色
     */
    @Override
    @Transactional
    public AjaxResult insertGuideRole(SysRole role) {
        // 新增角色信息
        roleMapper.insertRole(role);
        //新增角色菜单
        insertRoleMenu(role);
        //新增角色指标
        if (role.getIndexIds().length > 0){
            insertAllocationIndex(role.getRoleId(),role.getIndexIds());
        }
        //新增数据权限
        if (role.getIndexIds().length > 0){
            insertRoleRegion(role);
        }
        return AjaxResult.success();
    }

    @Override
    @Transactional
    public int updateGuideRole(SysRole role) {
        // 修改角色信息
        roleMapper.updateRole(role);
        // 删除角色与菜单关联
        roleMenuMapper.deleteRoleMenuByRoleId(role.getRoleId());
        //删除角色与地区关联
        roleRegionMapper.deleteRoleRegionByRoleId(role.getRoleId());
        //删除角色与指标关联
        sysRoleIndexMapper.deleteSysRoleIndexByRoleId(role.getRoleId());
        //新增角色地区关联
        if (role.getIndexIds().length > 0){
            insertRoleRegion(role);
        }
        //新增角色与指标
        insertAllocationIndex(role.getRoleId(),role.getIndexIds());
        return insertRoleMenu(role);//新增角色菜单
    }

    /**
     * 修改保存角色信息
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateRole(SysRole role)
    {
        // 修改角色信息
        roleMapper.updateRole(role);
        // 删除角色与菜单关联
        roleMenuMapper.deleteRoleMenuByRoleId(role.getRoleId());
        return insertRoleMenu(role);
    }

    /**
     * 修改角色状态
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    public int updateRoleStatus(SysRole role)
    {
        return roleMapper.updateRole(role);
    }

    /**
     * 修改数据权限信息
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    @Transactional
    public int authDataScope(SysRole role)
    {
        // 修改角色信息
        roleMapper.updateRole(role);
        // 删除角色与地市关联
        roleRegionMapper.deleteRoleRegionByRoleId(role.getRoleId());
        // 新增角色和地市信息（数据权限）
        return insertRoleRegion(role);
    }

    /**
     * 新增角色菜单信息
     *
     * @param role 角色对象
     */
    public int insertRoleMenu(SysRole role)
    {
        int rows = 1;
        // 新增用户与角色管理
        List<SysRoleMenu> list = new ArrayList<>();
        for (Long menuId : role.getMenuIds())
        {
            SysRoleMenu rm = new SysRoleMenu();
            rm.setRoleId(role.getRoleId());
            rm.setMenuId(menuId);
            list.add(rm);
        }
        if (list.size() > 0)
        {
            rows = roleMenuMapper.batchRoleMenu(list);
        }
        return rows;
    }

    /**
     * 新增角色地市信息(数据权限)
     *
     * @param role 角色对象
     */
    public int insertRoleRegion(SysRole role)
    {
        int rows = 1;
            // 新增角色与地市（数据权限）管理
            List<SysRoleRegion> list = new ArrayList<>();

            if (Objects.nonNull(role.getRegionIds())){
                for (String regId : role.getRegionIds())
                {
                    SysRoleRegion rd = new SysRoleRegion();
                    rd.setRoleId(role.getRoleId());
                    rd.setRegId(regId);
                    list.add(rd);
                }
                if (list.size() > 0)
                {
                    rows = roleRegionMapper.batchRoleRegion(list);
                }
            }
        return rows;
    }

    /**
     * 通过角色ID删除角色
     *
     * @param roleId 角色ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteRoleById(Long roleId)
    {
        // 删除角色与菜单关联
        roleMenuMapper.deleteRoleMenuByRoleId(roleId);
        // 删除角色与地市关联
        roleRegionMapper.deleteRoleRegionByRoleId(roleId);
        return roleMapper.deleteRoleById(roleId);
    }

    /**
     * 批量删除角色信息
     *
     * @param roleIds 需要删除的角色ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteRoleByIds(Long[] roleIds)
    {
        for (Long roleId : roleIds)
        {
            checkRoleAllowed(new SysRole(roleId));
            checkRoleDataScope(roleId);
            SysRole role = selectRoleById(roleId);
            if (countUserRoleByRoleId(roleId) > 0)
            {
                throw new ServiceException(String.format("%1$s已分配,不能删除", role.getRoleName()));
            }
        }
        // 删除角色与菜单关联
        roleMenuMapper.deleteRoleMenu(roleIds);
        // 删除角色与地市关联
        roleRegionMapper.deleteRoleRegion(roleIds);
        //删除角色与指标关联
        sysRoleIndexMapper.deleteSysRoleIndexByRoleIds(roleIds);
        return roleMapper.deleteRoleByIds(roleIds);
    }

    /**
     * 取消授权用户角色
     *
     * @param userRole 用户和角色关联信息
     * @return 结果
     */
    @Override
    public int deleteAuthUser(SysUserRole userRole)
    {
        return userRoleMapper.deleteUserRoleInfo(userRole);
    }

    /**
     * 批量取消授权用户角色
     *
     * @param roleId 角色ID
     * @param userIds 需要取消授权的用户数据ID
     * @return 结果
     */
    @Override
    public int deleteAuthUsers(Long roleId, Long[] userIds)
    {
        return userRoleMapper.deleteUserRoleInfos(roleId, userIds);
    }

    /**
     * 批量选择授权用户角色
     *
     * @param roleId 角色ID
     * @param userIds 需要授权的用户数据ID
     * @return 结果
     */
    @Override
    public int insertAuthUsers(Long roleId, Long[] userIds)
    {
        // 新增用户与角色管理
        List<SysUserRole> list = new ArrayList<>();
        for (Long userId : userIds)
        {
            SysUserRole ur = new SysUserRole();
            ur.setUserId(userId);
            ur.setRoleId(roleId);
            list.add(ur);
        }
        return userRoleMapper.batchUserRole(list);
    }

    /**
     * 批量授权角色指示看板
     * @param roleId
     * @param indexIds
     * @return
     */
    @Override
    public int insertAllocationIndex(Long roleId, Long[] indexIds) {
        List<SysRoleIndex> list = new ArrayList<>();
        if (indexIds.length>0){
            for (Long indexId : indexIds) {
                SysRoleIndex sysRoleIndex = new SysRoleIndex();
                sysRoleIndex.setRoleId(roleId);
                sysRoleIndex.setIndexId(indexId);
                list.add(sysRoleIndex);
            }
            return sysRoleIndexMapper.batchRoleIndex(list);
        }
        return 0;
    }
    /**
     * 取消指标授权用户
     */
    @Override
    public int deleteIndex(SysRoleIndex roleIndex) {
        return sysRoleIndexMapper.deleteRoleIndex(roleIndex);
    }
    /**
     * 批量取消指标授权
     */
    @Override
    public int deleteIndexAuthUsers(Long roleId, Long[] indexIds) {
        return sysRoleIndexMapper.deleteBatch(roleId,indexIds);
    }


}
