package com.rzl.workbenche.system.mapper;

import java.util.List;
import com.rzl.workbenche.system.domain.SysRoleIndex;
import org.apache.ibatis.annotations.Param;

/**
 * 角色和指示看板关联Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-31
 */
public interface SysRoleIndexMapper
{
    /**
     * 查询角色和指示看板关联
     *
     * @param roleId 角色和指示看板关联主键
     * @return 角色和指示看板关联
     */
    public List<SysRoleIndex> selectSysRoleIndexByRoleId(Long roleId);

    /**
     * 查询角色和指示看板关联列表
     *
     * @param sysRoleIndex 角色和指示看板关联
     * @return 角色和指示看板关联集合
     */
    public List<SysRoleIndex> selectSysRoleIndexList(SysRoleIndex sysRoleIndex);

    /**
     * 新增角色和指示看板关联
     *
     * @param sysRoleIndex 角色和指示看板关联
     * @return 结果
     */
    public int insertSysRoleIndex(SysRoleIndex sysRoleIndex);

    /**
     * 修改角色和指示看板关联
     *
     * @param sysRoleIndex 角色和指示看板关联
     * @return 结果
     */
    public int updateSysRoleIndex(SysRoleIndex sysRoleIndex);

    /**
     * 删除角色和指示看板关联
     *
     * @param roleId 角色和指示看板关联主键
     * @return 结果
     */
    public int deleteSysRoleIndexByRoleId(Long roleId);

    /**
     * 批量删除角色和指示看板关联
     *
     * @param roleIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysRoleIndexByRoleIds(Long[] roleIds);
    /**
     * 批量添加角色
     */
    public int batchRoleIndex(List<SysRoleIndex> roleIndexList);
    /**
     * 取消指标授权用户
     */
    int deleteRoleIndex(SysRoleIndex roleIndex);
    /**
     * 批量取消指标授权
     */
    int deleteBatch(@Param("roleId") Long roleId, @Param("indexIds") Long[] indexIds);

    /**
     * 通过角色查询指标权限
     * @param roleIds
     * @return
     */
    List<Long> selectIndexIds(@Param("roleIds") List<Long> roleIds);
}
