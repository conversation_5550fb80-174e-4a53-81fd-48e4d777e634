package com.rzl.workbenche.system.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import com.alibaba.fastjson.JSON;
import com.rzl.workbenche.common.config.CacheConfig;
import com.rzl.workbenche.common.config.TenantContextHolder;
import com.rzl.workbenche.common.constant.ApiConstants;
import com.rzl.workbenche.common.constant.UserConstants;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.exception.ServiceException;
import com.rzl.workbenche.common.utils.DateTimeUtils;
import com.rzl.workbenche.common.utils.SecurityUtils;
import com.rzl.workbenche.common.utils.StrUtils;
import com.rzl.workbenche.common.utils.http.ApiUtils;
import com.rzl.workbenche.ops.domain.BusinessDataConfig;
import com.rzl.workbenche.ops.mapper.BusinessDataConfigMapper;
import com.rzl.workbenche.system.domain.dto.SysUserBindDto;
import jdk.internal.dynalink.beans.StaticClass;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rzl.workbenche.system.mapper.SysUserBindMapper;
import com.rzl.workbenche.system.domain.SysUserBind;
import com.rzl.workbenche.system.service.ISysUserBindService;
import org.springframework.util.CollectionUtils;

/**
 * 用户系统绑定Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-12
 */
@Service
public class SysUserBindServiceImpl implements ISysUserBindService
{
    @Autowired
    private SysUserBindMapper sysUserBindMapper;

    @Autowired
    private BusinessDataConfigMapper dataConfigMapper;

    private static final String USERNAME = "username";

    private static final String PASSWORD = "password";
    /**
     * 查询用户系统绑定
     *
     * @param id 用户系统绑定主键
     * @return 用户系统绑定
     */
    @Override
    public SysUserBind selectSysUserBindById(Long id)
    {
        return sysUserBindMapper.selectSysUserBindById(id);
    }

    /**
     * 查询用户系统绑定列表
     *
     * @param sysUserBind 用户系统绑定
     * @return 用户系统绑定
     */
    @Override
    public List<SysUserBind> selectSysUserBindList(SysUserBind sysUserBind)
    {
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserId())) {
            sysUserBind.setUserId(SecurityUtils.getUserId());
            return sysUserBindMapper.selectSysUserBindList(sysUserBind);
        }
        return sysUserBindMapper.selectSysUserBindList(sysUserBind);
    }

    /**
     * 新增用户系统绑定
     *
     * @param sysUserBind 用户系统绑定
     * @return 结果
     */
    @Override
    public int insertSysUserBind(SysUserBind sysUserBind)
    {

        return sysUserBindMapper.insertSysUserBind(sysUserBind);
    }

    /**
     * 修改用户系统绑定
     *
     * @param sysUserBind 用户系统绑定
     * @return 结果
     */
    @Override
    public int updateSysUserBind(SysUserBind sysUserBind)
    {
        return sysUserBindMapper.updateSysUserBind(sysUserBind);
    }

    /**
     * 批量删除用户系统绑定
     *
     * @param ids 需要删除的用户系统绑定主键
     * @return 结果
     */
    @Override
    public int deleteSysUserBindByIds(Long[] ids)
    {
        return sysUserBindMapper.deleteSysUserBindByIds(ids);
    }

    /**
     * 删除用户系统绑定信息
     *
     * @param id 用户系统绑定主键
     * @return 结果
     */
    @Override
    public int deleteSysUserBindById(Long id)
    {
        SysUserBind sysUserBind = sysUserBindMapper.selectSysUserBindById(id);
        if ("1".equals(sysUserBind.getAccountStatus())){
            throw new ServiceException("请禁用后删除");
        }
        return sysUserBindMapper.deleteSysUserBindById(id);
    }
    /**
     * 绑定账户
     * @param sysUserBindDto
     * @return
     */
    @Override
    public AjaxResult bind(SysUserBindDto sysUserBindDto) {
        if (StrUtils.isEmpty(sysUserBindDto.getBindUser())||!UserConstants.USERNAME.matcher(sysUserBindDto.getBindUser()).matches()){
            throw new ServiceException("登录账号格式错误，请输入以字母开头的5-16位账号");
        }
        if (StrUtils.isEmpty(sysUserBindDto.getPassword())||!UserConstants.PASSWORD.matcher(sysUserBindDto.getPassword()).matches()){
            throw new ServiceException("用户密码8-20位，由数字、大写字母、小写字母、特殊字符三种组成！");
        }
        //查询用户是否绑定该系统
        sysUserBindDto.setUserId(SecurityUtils.getUserId());
        SysUserBind sysUserBind = sysUserBindMapper.selectSysUserBind(sysUserBindDto);
        if (Objects.nonNull(sysUserBind)){
            return AjaxResult.error("用户已绑定该系统");
        }
        Map<String, String> map = new HashMap<>();
        map.put(USERNAME,sysUserBindDto.getBindUser());
        map.put(PASSWORD,sysUserBindDto.getPassword());
        //查询地址
        try {
            if (!isSuccess(sysUserBindDto.getSystem(), map)){
                return AjaxResult.error("用户绑定失败");
            }
            SysUserBind userBind = new SysUserBind();
            //绑定用户
            BeanUtils.copyProperties(sysUserBindDto,userBind);
            userBind.setUserId(SecurityUtils.getUserId());
            userBind.setAccountStatus("1");
            userBind.setBindTime(DateTimeUtils.getNowDate());
            sysUserBindMapper.insertSysUserBind(userBind);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("绑定失败");
        }
        return AjaxResult.success();
    }

    private boolean isSuccess(String system, Map<String, String> map) throws Exception {
        BusinessDataConfig dataConfig = dataConfigMapper.selectBySystem(system);
        String systemUrl = dataConfig.getSystemUrl();
        //菜单地址
        String bindUrl = systemUrl + ApiConstants.API_ACCOUNT_BIND_URL;
        String domain = CacheConfig.getDomain(TenantContextHolder.getTenant());
        return ApiUtils.post(bindUrl, system, domain, dataConfig.getSysPub(), JSON.toJSONString(map));
//        return true;
    }

    /**
     * 解绑
     * @param userBindDto
     * @return
     */
    @Override
    public AjaxResult unBind(SysUserBindDto userBindDto) {
        try {
            SysUserBind sysUserBind = sysUserBindMapper.selectSysUserBindById(userBindDto.getId());
            if ("1".equals(sysUserBind.getAccountStatus())){
                return AjaxResult.error("请禁用后再解绑");
            }
            Map<String, String> map = new HashMap<>();
            map.put(USERNAME,sysUserBind.getBindUser());
            map.put(PASSWORD,userBindDto.getPassword());
            //调用 解绑接口
            if (!isSuccess(sysUserBind.getSystem(), map)){
                return AjaxResult.error("解绑失败");
            }
            sysUserBindMapper.deleteSysUserBindById(userBindDto.getId());
            return AjaxResult.success();
        } catch (Exception e) {
            throw new ServiceException("解绑失败");
        }
    }

    @Override
    public AjaxResult findByUserId(Long userId) {
        List<SysUserBind> userBinds = sysUserBindMapper.findByUserId(userId);
        return AjaxResult.success(userBinds);
    }

    @Override
    public AjaxResult checkUserBind(SysUserBind bind) {
        try {
            if (StrUtils.isEmpty(bind.getBindUser())||!UserConstants.USERNAME.matcher(bind.getBindUser()).matches()){
                throw new ServiceException("登录账号格式错误，请输入以字母开头的5-16位账号");
            }
            if (StrUtils.isEmpty(bind.getBindPassword())||!UserConstants.PASSWORD.matcher(bind.getBindPassword()).matches()){
                throw new ServiceException("用户密码8-20位，由数字、大写字母、小写字母、特殊字符三种组成！");
            }
            List<SysUserBind> userBinds = sysUserBindMapper.selectSysAndUser(bind);
            if (!CollectionUtils.isEmpty(userBinds)){
                return AjaxResult.error("该系统用户名已被绑定");
            }
            Map<String, String> map = new HashMap<>();
            map.put(USERNAME,bind.getBindUser());
            map.put(PASSWORD,bind.getBindPassword());
            //调用 解绑接口
            if (!isSuccess(bind.getSystem(), map)){
                return AjaxResult.error("用户校验失败");
            }
        } catch (Exception e) {
            throw new ServiceException("用户校验失败");
        }
        return AjaxResult.success(bind);
    }

}
