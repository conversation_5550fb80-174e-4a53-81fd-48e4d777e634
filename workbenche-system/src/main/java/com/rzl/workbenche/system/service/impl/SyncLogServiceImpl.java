package com.rzl.workbenche.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rzl.workbenche.system.mapper.SyncLogMapper;
import com.rzl.workbenche.system.domain.SyncLog;
import com.rzl.workbenche.system.service.ISyncLogService;

/**
 * 同步接口日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-09-11
 */
@Service
public class SyncLogServiceImpl implements ISyncLogService 
{
    @Autowired
    private SyncLogMapper syncLogMapper;

    /**
     * 查询同步接口日志
     * 
     * @param id 同步接口日志主键
     * @return 同步接口日志
     */
    @Override
    public SyncLog selectSyncLogById(Long id)
    {
        return syncLogMapper.selectSyncLogById(id);
    }

    /**
     * 查询同步接口日志列表
     * 
     * @param syncLog 同步接口日志
     * @return 同步接口日志
     */
    @Override
    public List<SyncLog> selectSyncLogList(SyncLog syncLog)
    {
        return syncLogMapper.selectSyncLogList(syncLog);
    }

    /**
     * 新增同步接口日志
     * 
     * @param syncLog 同步接口日志
     * @return 结果
     */
    @Override
    public int insertSyncLog(SyncLog syncLog)
    {
        return syncLogMapper.insertSyncLog(syncLog);
    }

    /**
     * 修改同步接口日志
     * 
     * @param syncLog 同步接口日志
     * @return 结果
     */
    @Override
    public int updateSyncLog(SyncLog syncLog)
    {
        return syncLogMapper.updateSyncLog(syncLog);
    }

    /**
     * 批量删除同步接口日志
     * 
     * @param ids 需要删除的同步接口日志主键
     * @return 结果
     */
    @Override
    public int deleteSyncLogByIds(Long[] ids)
    {
        return syncLogMapper.deleteSyncLogByIds(ids);
    }

    /**
     * 删除同步接口日志信息
     * 
     * @param id 同步接口日志主键
     * @return 结果
     */
    @Override
    public int deleteSyncLogById(Long id)
    {
        return syncLogMapper.deleteSyncLogById(id);
    }
}