package com.rzl.workbenche.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.rzl.workbenche.common.annotation.Excel;
import com.rzl.workbenche.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 推送日志对象 push_log
 * 
 * <AUTHOR>
 * @date 2023-09-07
 */
public class PushLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 请求参数 */
    @Excel(name = "采集参数")
    private String param;

    /** 推送时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "采集时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date pushTime;

    /** 调用状态 */
    @Excel(name = "采集状态",dictType = "sys_common_status")
    private String status;

    /** 耗时 */
    @Excel(name = "耗时(毫秒)")
    private Long duration;

    /** 接口名称 */
    @Excel(name = "接口名称")
    private String name;
    /**
     * 请求方式
     */
    @Excel(name = "接口类型")
    private String type;

    /**
     * 业务系统
     * @return
     */
    private String system;

    /**
     * 失败原因
     * @return
     */
    private String reason;

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getSystem() {
        return system;
    }

    public void setSystem(String system) {
        this.system = system;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setParam(String param)
    {
        this.param = param;
    }

    public String getParam()
    {
        return param;
    }
    public void setPushTime(Date pushTime) 
    {
        this.pushTime = pushTime;
    }

    public Date getPushTime() 
    {
        return pushTime;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setDuration(Long duration) 
    {
        this.duration = duration;
    }

    public Long getDuration() 
    {
        return duration;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("params", getParams())
            .append("pushTime", getPushTime())
            .append("status", getStatus())
            .append("duration", getDuration())
            .append("name", getName())
            .toString();
    }
}