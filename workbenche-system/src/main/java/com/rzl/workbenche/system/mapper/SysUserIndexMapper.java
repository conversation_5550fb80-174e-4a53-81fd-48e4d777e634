package com.rzl.workbenche.system.mapper;

import java.util.List;

import com.rzl.workbenche.system.domain.SysUserIndex;
import org.apache.ibatis.annotations.Param;

/**
 * 用户和指标关系Mapper接口
 *
 * <AUTHOR>
 * @date 2023-09-07
 */
public interface SysUserIndexMapper
{
    /**
     * 查询用户和指标关系
     *
     * @param userId 用户和指标关系主键
     * @return 用户和指标关系
     */
    public SysUserIndex selectSysUserIndexByUserId(Long userId);

    /**
     * 查询用户和指标关系列表
     *
     * @param sysUserIndex 用户和指标关系
     * @return 用户和指标关系集合
     */
    public List<SysUserIndex> selectSysUserIndexList(SysUserIndex sysUserIndex);

    /**
     * 新增用户和指标关系
     *
     * @param sysUserIndex 用户和指标关系
     * @return 结果
     */
    public int insertSysUserIndex(SysUserIndex sysUserIndex);

    /**
     * 修改用户和指标关系
     *
     * @param sysUserIndex 用户和指标关系
     * @return 结果
     */
    public int updateSysUserIndex(SysUserIndex sysUserIndex);

    /**
     * 删除用户和指标关系
     *
     * @param userId 用户和指标关系主键
     * @return 结果
     */
    public int deleteSysUserIndexByUserId(Long userId);

    /**
     * 批量删除用户和指标关系
     *
     * @param userIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysUserIndexByUserIds(Long[] userIds);
    /**
     * 新增用户指标排序
     */
    void insertBatch(@Param("userIndexs") List<SysUserIndex> userIndexs);

    void deleteSysUserIndex(SysUserIndex userIndex);

    /**
     * 查询排序
     * @param userId
     * @param indexId
     * @return
     */
    SysUserIndex selectIndexOrder(@Param("userId") Long userId, @Param("indexId") Long indexId);
}
