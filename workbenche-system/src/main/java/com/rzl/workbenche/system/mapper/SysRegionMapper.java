package com.rzl.workbenche.system.mapper;

import com.rzl.workbenche.common.core.domain.entity.SysRegion;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 地市管理 数据层
 *
 * <AUTHOR>
 */
public interface SysRegionMapper
{
    /**
     * 查询地市管理数据
     *
     * @param region 地市信息
     * @return 地市信息集合
     */
    public List<SysRegion> selectRegionList(SysRegion region);

    /**
     * 根据角色ID查询地市树信息
     *
     * @param roleId 角色ID
     * @param regionCheckStrictly 地市树选择项是否关联显示
     * @return 选中地市列表
     */
    public List<String> selectRegionListByRoleId(@Param("roleId") Long roleId, @Param("regionCheckStrictly") boolean regionCheckStrictly);

    /**
     * 根据地市ID查询信息
     *
     * @param regionId 地市ID
     * @return 地市信息
     */
    public SysRegion selectRegionById(String regionId);

    /**
     * 根据ID查询所有子地市
     *
     * @param regionId 地市ID
     * @return 地市列表
     */
    public List<SysRegion> selectChildrenRegionById(String regionId);

    /**
     * 根据ID查询所有子地市（正常状态）
     *
     * @param regionId 地市ID
     * @return 子地市数
     */
    public int selectNormalChildrenRegionById(String regionId);

    /**
     * 是否存在子节点
     *
     * @param regionId 地市ID
     * @return 结果
     */
    public int hasChildByRegionId(String regionId);

    /**
     * 查询地市是否存在用户
     *
     * @param regionId 地市ID
     * @return 结果
     */
    public int checkRegionExistUser(String regionId);

    /**
     * 校验地市名称是否唯一
     *
     * @param regName 地市名称
     * @param parentId 父地市ID
     * @return 结果
     */
    public SysRegion checkRegionNameUnique(@Param("regName") String regName, @Param("parentId") String parentId);

    /**
     * 新增地市信息
     *
     * @param region 地市信息
     * @return 结果
     */
    public int insertRegion(SysRegion region);

    /**
     * 修改地市信息
     *
     * @param region 地市信息
     * @return 结果
     */
    public int updateRegion(SysRegion region);

    /**
     * 修改所在地市正常状态
     *
     * @param regionIds 地市ID组
     */
    public void updateRegionStatusNormal(String[] regionIds);

    /**
     * 修改子元素关系
     *
     * @param regions 子元素
     * @return 结果
     */
    public int updateRegionChildren(@Param("regions") List<SysRegion> regions);

    /**
     * 删除地市管理信息
     *
     * @param regionId 地市ID
     * @return 结果
     */
    public int deleteRegionById(String regionId);

    SysRegion selectByRegName(String regName);

    String selectRegIdByRegName(String regName);

    List<String> selectRegIds();
}
