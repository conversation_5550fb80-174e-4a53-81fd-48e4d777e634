package com.rzl.workbenche.system.service;

import java.util.List;

import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.system.domain.SysUserBind;
import com.rzl.workbenche.system.domain.dto.SysUserBindDto;

/**
 * 用户系统绑定Service接口
 * 
 * <AUTHOR>
 * @date 2023-09-12
 */
public interface ISysUserBindService 
{
    /**
     * 查询用户系统绑定
     * 
     * @param id 用户系统绑定主键
     * @return 用户系统绑定
     */
    public SysUserBind selectSysUserBindById(Long id);

    /**
     * 查询用户系统绑定列表
     * 
     * @param sysUserBind 用户系统绑定
     * @return 用户系统绑定集合
     */
    public List<SysUserBind> selectSysUserBindList(SysUserBind sysUserBind);

    /**
     * 新增用户系统绑定
     * 
     * @param sysUserBind 用户系统绑定
     * @return 结果
     */
    public int insertSysUserBind(SysUserBind sysUserBind);

    /**
     * 修改用户系统绑定
     * 
     * @param sysUserBind 用户系统绑定
     * @return 结果
     */
    public int updateSysUserBind(SysUserBind sysUserBind);

    /**
     * 批量删除用户系统绑定
     * 
     * @param ids 需要删除的用户系统绑定主键集合
     * @return 结果
     */
    public int deleteSysUserBindByIds(Long[] ids);

    /**
     * 删除用户系统绑定信息
     * 
     * @param id 用户系统绑定主键
     * @return 结果
     */
    public int deleteSysUserBindById(Long id);

    /**
     * 绑定账户
     * @param sysUserBindDto
     * @return
     */
    AjaxResult bind(SysUserBindDto sysUserBindDto);

    /**
     * 解绑
     * @param userBindDto
     * @return
     */
    AjaxResult unBind(SysUserBindDto userBindDto);

    AjaxResult findByUserId(Long userId);

    AjaxResult checkUserBind(SysUserBind bind);
}