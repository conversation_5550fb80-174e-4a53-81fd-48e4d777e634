package com.rzl.workbenche.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rzl.workbenche.system.mapper.PushLogMapper;
import com.rzl.workbenche.system.domain.PushLog;
import com.rzl.workbenche.system.service.IPushLogService;

/**
 * 推送日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-09-07
 */
@Service
public class PushLogServiceImpl implements IPushLogService 
{
    @Autowired
    private PushLogMapper pushLogMapper;

    /**
     * 查询推送日志
     * 
     * @param id 推送日志主键
     * @return 推送日志
     */
    @Override
    public PushLog selectPushLogById(Long id)
    {
        return pushLogMapper.selectPushLogById(id);
    }

    /**
     * 查询推送日志列表
     * 
     * @param pushLog 推送日志
     * @return 推送日志
     */
    @Override
    public List<PushLog> selectPushLogList(PushLog pushLog)
    {
        return pushLogMapper.selectPushLogList(pushLog);
    }

    /**
     * 新增推送日志
     * 
     * @param pushLog 推送日志
     * @return 结果
     */
    @Override
    public int insertPushLog(PushLog pushLog)
    {
        return pushLogMapper.insertPushLog(pushLog);
    }

    /**
     * 修改推送日志
     * 
     * @param pushLog 推送日志
     * @return 结果
     */
    @Override
    public int updatePushLog(PushLog pushLog)
    {
        return pushLogMapper.updatePushLog(pushLog);
    }

    /**
     * 批量删除推送日志
     * 
     * @param ids 需要删除的推送日志主键
     * @return 结果
     */
    @Override
    public int deletePushLogByIds(Long[] ids)
    {
        return pushLogMapper.deletePushLogByIds(ids);
    }

    /**
     * 删除推送日志信息
     * 
     * @param id 推送日志主键
     * @return 结果
     */
    @Override
    public int deletePushLogById(Long id)
    {
        return pushLogMapper.deletePushLogById(id);
    }
}
