package com.rzl.workbenche.system.service.impl;

import com.rzl.workbenche.common.annotation.DataScope;
import com.rzl.workbenche.common.constant.UserConstants;
import com.rzl.workbenche.common.core.domain.TreeSelect;
import com.rzl.workbenche.common.core.domain.entity.SysRegion;
import com.rzl.workbenche.common.core.domain.entity.SysRole;
import com.rzl.workbenche.common.core.domain.entity.SysUser;
import com.rzl.workbenche.common.core.text.Convert;
import com.rzl.workbenche.common.exception.ServiceException;
import com.rzl.workbenche.common.utils.SecurityUtils;
import com.rzl.workbenche.common.utils.StrUtils;
import com.rzl.workbenche.common.utils.spring.SpringUtils;
import com.rzl.workbenche.common.utils.uuid.IdUtils;
import com.rzl.workbenche.common.utils.uuid.UUID;
import com.rzl.workbenche.system.mapper.SysRegionMapper;
import com.rzl.workbenche.system.mapper.SysRoleMapper;
import com.rzl.workbenche.system.service.ISysRegionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 地市管理 服务实现
 *
 * <AUTHOR>
 */
@Service
public class SysRegionServiceImpl implements ISysRegionService
{
    @Autowired
    private SysRegionMapper regionMapper;

    @Autowired
    private SysRoleMapper roleMapper;

    /**
     * 查询地市管理数据
     *
     * @param region 地市信息
     * @return 地市信息集合r
     */
    @Override
    @DataScope(regionAlias = "r")
    public List<SysRegion> selectRegionList(SysRegion region)
    {
        return regionMapper.selectRegionList(region);
    }

    /**
     * 查询地市树结构信息
     *
     * @param region 地市信息
     * @return 地市树信息集合
     */
    @Override
    public List<TreeSelect> selectRegionTreeList(SysRegion region)
    {
        List<SysRegion> regions = SpringUtils.getAopProxy(this).selectRegionList(region);
        return buildRegionTreeSelect(regions);
    }

    /**
     * 构建前端所需要树结构
     *
     * @param regions 地市列表
     * @return 树结构列表
     */
    @Override
    public List<SysRegion> buildRegionTree(List<SysRegion> regions)
    {
        List<SysRegion> returnList = new ArrayList<>();
        List<String> tempList = regions.stream().map(SysRegion::getRegId).collect(Collectors.toList());
        for (SysRegion region : regions)
        {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(region.getParentId().trim()))
            {
                recursionFn(regions, region);
                returnList.add(region);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = regions;
        }
        return returnList;
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param regions 地市列表
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelect> buildRegionTreeSelect(List<SysRegion> regions)
    {
        List<SysRegion> regionTrees = buildRegionTree(regions);
        return regionTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 根据角色ID查询地市树信息
     *
     * @param roleId 角色ID
     * @return 选中地市列表
     */
    @Override
    public List<String> selectRegionListByRoleId(Long roleId)
    {
        SysRole role = roleMapper.selectRoleById(roleId);
        return regionMapper.selectRegionListByRoleId(roleId, role.isRegionCheckStrictly());
    }

    /**
     * 根据地市ID查询信息
     *
     * @param regionId 地市ID
     * @return 地市信息
     */
    @Override
    public SysRegion selectRegionById(String regionId)
    {
        return regionMapper.selectRegionById(regionId);
    }

    /**
     * 根据ID查询所有子地市（正常状态）
     *
     * @param regionId 地市ID
     * @return 子地市数
     */
    @Override
    public int selectNormalChildrenRegionById(String regionId)
    {
        return regionMapper.selectNormalChildrenRegionById(regionId);
    }

    /**
     * 是否存在子节点
     *
     * @param regionId 地市ID
     * @return 结果
     */
    @Override
    public boolean hasChildByRegionId(String regionId)
    {
        int result = regionMapper.hasChildByRegionId(regionId);
        return result > 0;
    }

    /**
     * 查询地市是否存在用户
     *
     * @param regionId 地市ID
     * @return 结果 true 存在 false 不存在
     */
    @Override
    public boolean checkRegionExistUser(String regionId)
    {
        int result = regionMapper.checkRegionExistUser(regionId);
        return result > 0;
    }

    /**
     * 校验地市名称是否唯一
     *
     * @param region 地市信息
     * @return 结果
     */
    @Override
    public String checkRegionNameUnique(SysRegion region)
    {
        String regionId = StrUtils.isNull(region.getRegId()) ? "-1" : region.getRegId();
        SysRegion info = regionMapper.checkRegionNameUnique(region.getRegName(), region.getParentId());
        if (StrUtils.isNotNull(info) && info.getRegId() != regionId)
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 修改校验地市
     * @param region
     * @return
     */
    public String checkRegionName(SysRegion region)
    {
        String regionId = StrUtils.isNull(region.getRegId()) ? "-1" : region.getRegId();
        if (region.getRegName().equals(regionMapper.selectRegionById(regionId).getRegName())){
            return UserConstants.UNIQUE;
        }
        SysRegion info = regionMapper.checkRegionNameUnique(region.getRegName(), region.getParentId());
        if (StrUtils.isNotNull(info) && info.getRegId() != regionId)
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }
    /**
     * 校验地市是否有数据权限
     *
     * @param regionId 地市id
     */
    @Override
    public void checkRegionDataScope(String regionId)
    {
        if (!SysUser.isAdmin(SecurityUtils.getUserId()))
        {
            SysRegion region = new SysRegion();
            region.setRegId(regionId);
            List<SysRegion> regions = SpringUtils.getAopProxy(this).selectRegionList(region);
            if (StrUtils.isEmpty(regions))
            {
                throw new ServiceException("没有权限访问地市数据！");
            }
        }
    }

    /**
     * 新增保存地市信息
     *
     * @param region 地市信息
     * @return 结果
     */
    @Override
    public int insertRegion(SysRegion region)
    {
        SysRegion info = regionMapper.selectRegionById(region.getParentId());
        // 如果父节点不为正常状态,则不允许新增子节点
        if (!UserConstants.DEPT_NORMAL.equals(info.getStatus()))
        {
            throw new ServiceException("地市停用，不允许新增");
        }
        region.setAncestors(info.getAncestors() + "," + region.getParentId());
        region.setRegId(IdUtils.simpleUUID());
        return regionMapper.insertRegion(region);
    }

    /**
     * 修改保存地市信息
     *
     * @param region 地市信息
     * @return 结果
     */
    @Override
    public int updateRegion(SysRegion region)
    {
        SysRegion newParentRegion = regionMapper.selectRegionById(region.getParentId());
        SysRegion oldRegion = regionMapper.selectRegionById(region.getRegId());
        if (StrUtils.isNotNull(newParentRegion) && StrUtils.isNotNull(oldRegion))
        {
            String newAncestors = newParentRegion.getAncestors() + "," + newParentRegion.getRegId();
            String oldAncestors = oldRegion.getAncestors();
            region.setAncestors(newAncestors);
            updateRegionChildren(region.getRegId(), newAncestors, oldAncestors);
        }
        int result = regionMapper.updateRegion(region);
        if (UserConstants.DEPT_NORMAL.equals(region.getStatus()) && StrUtils.isNotEmpty(region.getAncestors())
                && !StrUtils.equals("0", region.getAncestors()))
        {
            // 如果该地市是启用状态，则启用该地市的所有上级地市
            updateParentRegionStatusNormal(region);
        }
        return result;
    }

    /**
     * 修改该地市的父级地市状态
     *
     * @param region 当前地市
     */
    private void updateParentRegionStatusNormal(SysRegion region)
    {
        String ancestors = region.getAncestors();
        String[] regionIds = Convert.toStrArray(ancestors);
        regionMapper.updateRegionStatusNormal(regionIds);
    }

    /**
     * 修改子元素关系
     *
     * @param regionId 被修改的地市ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateRegionChildren(String regionId, String newAncestors, String oldAncestors)
    {
        List<SysRegion> children = regionMapper.selectChildrenRegionById(regionId);
        for (SysRegion child : children)
        {
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
        }
        if (children.size() > 0)
        {
            regionMapper.updateRegionChildren(children);
        }
    }

    /**
     * 删除地市管理信息
     *
     * @param regionId 地市ID
     * @return 结果
     */
    @Override
    public int deleteRegionById(String regionId)
    {
        return regionMapper.deleteRegionById(regionId);
    }

    @Override
    public SysRegion selectByRegName(String regName) {
        return regionMapper.selectByRegName(regName);
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<SysRegion> list, SysRegion t)
    {
        // 得到子节点列表
        List<SysRegion> childList = getChildList(list, t);
        t.setChildren(childList);
        for (SysRegion tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<SysRegion> getChildList(List<SysRegion> list, SysRegion t)
    {
        List<SysRegion> tlist = new ArrayList<>();
        Iterator<SysRegion> it = list.iterator();
        while (it.hasNext())
        {
            SysRegion n = (SysRegion) it.next();
            if (StrUtils.isNotNull(n.getParentId()) && n.getParentId().equals(t.getRegId()))
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<SysRegion> list, SysRegion t)
    {
        return getChildList(list, t).size() > 0;
    }
}
