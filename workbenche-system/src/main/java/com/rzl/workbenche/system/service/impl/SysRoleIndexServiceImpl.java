package com.rzl.workbenche.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rzl.workbenche.system.mapper.SysRoleIndexMapper;
import com.rzl.workbenche.system.domain.SysRoleIndex;
import com.rzl.workbenche.system.service.ISysRoleIndexService;

/**
 * 角色和指示看板关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-31
 */
@Service
public class SysRoleIndexServiceImpl implements ISysRoleIndexService 
{
    @Autowired
    private SysRoleIndexMapper sysRoleIndexMapper;

    /**
     * 查询角色和指示看板关联
     * 
     * @param roleId 角色和指示看板关联主键
     * @return 角色和指示看板关联
     */
    @Override
    public List<SysRoleIndex> selectSysRoleIndexByRoleId(Long roleId)
    {
        return sysRoleIndexMapper.selectSysRoleIndexByRoleId(roleId);
    }

    /**
     * 查询角色和指示看板关联列表
     * 
     * @param sysRoleIndex 角色和指示看板关联
     * @return 角色和指示看板关联
     */
    @Override
    public List<SysRoleIndex> selectSysRoleIndexList(SysRoleIndex sysRoleIndex)
    {
        return sysRoleIndexMapper.selectSysRoleIndexList(sysRoleIndex);
    }

    /**
     * 新增角色和指示看板关联
     * 
     * @param sysRoleIndex 角色和指示看板关联
     * @return 结果
     */
    @Override
    public int insertSysRoleIndex(SysRoleIndex sysRoleIndex)
    {
        return sysRoleIndexMapper.insertSysRoleIndex(sysRoleIndex);
    }

    /**
     * 修改角色和指示看板关联
     * 
     * @param sysRoleIndex 角色和指示看板关联
     * @return 结果
     */
    @Override
    public int updateSysRoleIndex(SysRoleIndex sysRoleIndex)
    {
        return sysRoleIndexMapper.updateSysRoleIndex(sysRoleIndex);
    }

    /**
     * 批量删除角色和指示看板关联
     * 
     * @param roleIds 需要删除的角色和指示看板关联主键
     * @return 结果
     */
    @Override
    public int deleteSysRoleIndexByRoleIds(Long[] roleIds)
    {
        return sysRoleIndexMapper.deleteSysRoleIndexByRoleIds(roleIds);
    }

    /**
     * 删除角色和指示看板关联信息
     * 
     * @param roleId 角色和指示看板关联主键
     * @return 结果
     */
    @Override
    public int deleteSysRoleIndexByRoleId(Long roleId)
    {
        return sysRoleIndexMapper.deleteSysRoleIndexByRoleId(roleId);
    }
}
