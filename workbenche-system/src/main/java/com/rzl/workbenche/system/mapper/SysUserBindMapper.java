package com.rzl.workbenche.system.mapper;

import java.util.List;
import com.rzl.workbenche.system.domain.SysUserBind;
import com.rzl.workbenche.system.domain.dto.SysUserBindDto;
import org.apache.ibatis.annotations.Param;

/**
 * 用户系统绑定Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-09-12
 */
public interface SysUserBindMapper 
{
    /**
     * 查询用户系统绑定
     * 
     * @param id 用户系统绑定主键
     * @return 用户系统绑定
     */
    public SysUserBind selectSysUserBindById(Long id);

    /**
     * 查询用户系统绑定列表
     * 
     * @param sysUserBind 用户系统绑定
     * @return 用户系统绑定集合
     */
    public List<SysUserBind> selectSysUserBindList(SysUserBind sysUserBind);

    /**
     * 新增用户系统绑定
     * 
     * @param sysUserBind 用户系统绑定
     * @return 结果
     */
    public int insertSysUserBind(SysUserBind sysUserBind);

    /**
     * 修改用户系统绑定
     * 
     * @param sysUserBind 用户系统绑定
     * @return 结果
     */
    public int updateSysUserBind(SysUserBind sysUserBind);

    /**
     * 删除用户系统绑定
     * 
     * @param id 用户系统绑定主键
     * @return 结果
     */
    public int deleteSysUserBindById(Long id);

    /**
     * 批量删除用户系统绑定
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysUserBindByIds(Long[] ids);

    /**
     * 根据系统与用户名查询
     * @param sysUserBindDto
     * @return
     */
    SysUserBind selectSysUserBind(SysUserBindDto sysUserBindDto);

    void saveBatch(List<SysUserBind> userBinds);

    List<SysUserBind> findByUserId(Long userId);

    List<SysUserBind> selectSysAndUser(SysUserBind userBind);

    SysUserBind selectBySystemAndUserId(@Param("system") String system, @Param("userId") Long userId);
}