package com.rzl.workbenche.system.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.NotBlank;
import java.util.Date;

public class SysUserBindDto {
    private Long id;
    /**
     * 业务系统
     */
    @NotBlank(message = "业务系统不能为空")
    private String system;

    /** 主账号 */

    private String user;

    /** 绑定账号 */
    @NotBlank(message = "绑定账户不能为空")
    private String bindUser;

    /** 绑定参数 */

    private String bindParam;

    /** 账户类型 */
    @NotBlank(message = "账户类型不能为空")
    private String accountType;

    /** 账户状态 */
    private String accountStatus;

    /** 绑定时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date bindTime;

    /** 解绑时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date unBindTime;
    /**
     * 电话
     */
    private String phone;
    /**
     * 邮箱
     */
    private String email;

    /**
     * 密码
     * @return
     */
    private String password;


    private Long userId;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getSystem() {
        return system;
    }

    public void setSystem(String system) {
        this.system = system;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getBindUser() {
        return bindUser;
    }

    public void setBindUser(String bindUser) {
        this.bindUser = bindUser;
    }

    public String getBindParam() {
        return bindParam;
    }

    public void setBindParam(String bindParam) {
        this.bindParam = bindParam;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getAccountStatus() {
        return accountStatus;
    }

    public void setAccountStatus(String accountStatus) {
        this.accountStatus = accountStatus;
    }

    public Date getBindTime() {
        return bindTime;
    }

    public void setBindTime(Date bindTime) {
        this.bindTime = bindTime;
    }

    public Date getUnBindTime() {
        return unBindTime;
    }

    public void setUnBindTime(Date unBindTime) {
        this.unBindTime = unBindTime;
    }
}
