package com.rzl.workbenche.system.service.impl;

import java.util.*;
import java.util.stream.Collectors;
import javax.validation.Validator;

import cn.hutool.core.util.DesensitizedUtil;
import com.rzl.workbenche.common.annotation.DataScope;
import com.rzl.workbenche.common.constant.CacheConstants;
import com.rzl.workbenche.common.constant.Constants;
import com.rzl.workbenche.common.constant.UserConstants;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.core.domain.entity.SysRegion;
import com.rzl.workbenche.common.core.domain.entity.SysRole;
import com.rzl.workbenche.common.core.domain.entity.SysUser;
import com.rzl.workbenche.common.core.redis.RedisCache;
import com.rzl.workbenche.common.utils.DateTimeUtils;
import com.rzl.workbenche.common.utils.ParamParse;
import com.rzl.workbenche.common.utils.uuid.IdUtils;
import com.rzl.workbenche.ops.domain.vo.UserDataVo;
import com.rzl.workbenche.ops.domain.vo.UserVo;
import com.rzl.workbenche.system.domain.*;
import com.rzl.workbenche.system.domain.vo.SysUserVo;
import com.rzl.workbenche.common.exception.ServiceException;
import com.rzl.workbenche.common.utils.SecurityUtils;
import com.rzl.workbenche.common.utils.StrUtils;
import com.rzl.workbenche.common.utils.bean.BeanValidators;
import com.rzl.workbenche.common.utils.spring.SpringUtils;
import com.rzl.workbenche.system.mapper.*;
import com.rzl.workbenche.system.service.ISysConfigService;
import com.rzl.workbenche.system.service.ISysRegionService;
import com.rzl.workbenche.system.service.ISysUserService;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.impl.execchain.TunnelRefusedException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 用户 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysUserServiceImpl implements ISysUserService {
    private static final Logger log = LoggerFactory.getLogger(SysUserServiceImpl.class);

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SysPostMapper postMapper;

    @Autowired
    private SysUserRoleMapper userRoleMapper;

    @Autowired
    private SysUserPostMapper userPostMapper;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    protected Validator validator;

    @Autowired
    private ISysRegionService sysRegionService;

    @Autowired
    private SysUserBindMapper userBindMapper;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private SysEnumPwdMapper pwdMapper;
    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(regionAlias = "r", userAlias = "u")
    public List<SysUser> selectUserList(SysUser user) {
        List<SysUser> sysUsers = userMapper.selectUserList(user);
        sysUsers.forEach(sysUser -> {
            sysUser.setPhoneNumber(DesensitizedUtil.mobilePhone(sysUser.getPhoneNumber()));
            sysUser.setNickName(DesensitizedUtil.chineseName(sysUser.getNickName()));
            SysRegion sysRegion = sysRegionService.selectRegionById(sysUser.getRegId());
            if (StrUtils.isNotNull(sysRegion)){
                sysUser.setRegName(sysRegion.getRegName());
            }
        });
        return sysUsers;
    }

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(regionAlias = "r", userAlias = "u")
    public List<SysUser> selectAllocatedList(SysUser user) {
        return userMapper.selectAllocatedList(user);
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(regionAlias = "r", userAlias = "u")
    public List<SysUser> selectUnallocatedList(SysUser user) {
        return userMapper.selectUnallocatedList(user);
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByUserName(String userName) {
        return userMapper.selectUserByUserName(userName);
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserById(Long userId) {
        return userMapper.selectUserById(userId);
    }

    /**
     * 查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(String userName) {
        List<SysRole> list = roleMapper.selectRolesByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StrUtils.EMPTY;
        }
        return list.stream().map(SysRole::getRoleName).collect(Collectors.joining(","));
    }

    /**
     * 查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserPostGroup(String userName) {
        List<SysPost> list = postMapper.selectPostsByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StrUtils.EMPTY;
        }
        return list.stream().map(SysPost::getPostName).collect(Collectors.joining(","));
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public String checkUserNameUnique(SysUser user) {
        Long userId = StrUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkUserNameUnique(user.getUserName());
        if (StrUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public String checkPhoneUnique(SysUser user) {
        Long userId = StrUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkPhoneUnique(user.getPhoneNumber());
        if (StrUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public String checkEmailUnique(SysUser user) {
        Long userId = StrUtils.isNull(user.getUserId()) ? -1L : user.getUserId();
        SysUser info = userMapper.checkEmailUnique(user.getEmail());
        if (StrUtils.isNotNull(info) && info.getUserId().longValue() != userId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    @Override
    public void checkUserAllowed(SysUser user) {
        if (StrUtils.isNotNull(user.getUserId()) && user.isAdmin()) {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    @Override
    public void checkUserDataScope(Long userId) {
        if (!SysUser.isAdmin(SecurityUtils.getUserId())) {
            SysUser user = new SysUser();
            user.setUserId(userId);
            List<SysUser> users = SpringUtils.getAopProxy(this).selectUserList(user);
            if (StrUtils.isEmpty(users)) {
                throw new ServiceException("没有权限访问用户数据！");
            }
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertUser(SysUser user) {
        // 新增用户信息
        int rows = userMapper.insertUser(user);
        // 新增用户岗位关联
        insertUserPost(user);
        // 新增用户与角色管理
        insertUserRole(user);
        //存redis
        redisCache.setCacheObject(CacheConstants.LOGIN_TOKEN_KEY + user.getUserName(), IdUtils.fastUUID());
        return rows;
    }

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean registerUser(SysUser user) {
        return userMapper.insertUser(user) > 0;
    }

    /**
     * 修改保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateUser(SysUser user) {
        Long userId = user.getUserId();
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 新增用户与角色管理
        insertUserRole(user);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPostByUserId(userId);
        // 新增用户与岗位管理
        insertUserPost(user);

        return userMapper.updateUser(user);
    }

    @Override
    public int updateAuthUser(SysUser user) {
        return userMapper.updateUser(user);
    }

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    @Override
    @Transactional
    public void insertUserAuth(Long userId, Long[] roleIds) {
        userRoleMapper.deleteUserRoleByUserId(userId);
        insertUserRole(userId, roleIds);
    }

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserStatus(SysUser user) {
        return userMapper.updateUser(user);
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserProfile(SysUser user) {
        return userMapper.updateUser(user);
    }

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar) {
        return userMapper.updateUserAvatar(userName, avatar) > 0;
    }

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int resetPwd(SysUser user) {
        return userMapper.updateUser(user);
    }

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(String userName, String password) {
        return userMapper.resetUserPwd(userName, password);
    }

    /**
     * 新增用户角色信息
     *
     * @param user 用户对象
     */
    public void insertUserRole(SysUser user) {
        this.insertUserRole(user.getUserId(), user.getRoleIds());
    }

    /**
     * 新增用户岗位信息
     *
     * @param user 用户对象
     */
    public void insertUserPost(SysUser user) {
        Long[] posts = user.getPostIds();
        if (StrUtils.isNotEmpty(posts)) {
            // 新增用户与岗位管理
            List<SysUserPost> list = new ArrayList<>(posts.length);
            for (Long postId : posts) {
                SysUserPost up = new SysUserPost();
                up.setUserId(user.getUserId());
                up.setPostId(postId);
                list.add(up);
            }
            userPostMapper.batchUserPost(list);
        }
    }

    /**
     * 新增用户角色信息
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    public void insertUserRole(Long userId, Long[] roleIds) {
        if (StrUtils.isNotEmpty(roleIds)) {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<>(roleIds.length);
            for (Long roleId : roleIds) {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                list.add(ur);
            }
            userRoleMapper.batchUserRole(list);
        }
    }
    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteUserById(Long userId) {
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 删除用户与岗位表
        userPostMapper.deleteUserPostByUserId(userId);
        return userMapper.deleteUserById(userId);
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteUserByIds(Long[] userIds) {
        for (Long userId : userIds) {
            checkUserAllowed(new SysUser(userId));
            checkUserDataScope(userId);
        }
        // 删除用户与角色关联
        userRoleMapper.deleteUserRole(userIds);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPost(userIds);
        return userMapper.deleteUserByIds(userIds);
    }

    /**
     * 导入用户数据
     *
     * @param userList        用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    @Override
    public String importUser(List<SysUser> userList, Boolean isUpdateSupport, String operName) {
        if (StrUtils.isNull(userList) || userList.size() == 0) {
            throw new ServiceException("导入用户数据不能为空！");
        }
        List<String> phones = userList.stream().map(SysUser::getPhoneNumber).collect(Collectors.toList());
        Set<String> phoneList = new HashSet<>(phones);
        if (phones.size() != phoneList.size()){
            throw new ServiceException("电话号码重复");
        }
        for (String phone : phones) {
            SysUser sysUser = userMapper.selectUserByPhone(phone);
            if (StrUtils.isNotNull(sysUser)){
                throw new ServiceException("电话号码" + phone + "已存在" );
            }
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        String password = configService.selectConfigByKey("sys.user.initPassword");
        for (SysUser user : userList) {
            try {
                if (StrUtils.isEmpty(user.getRegName())){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账号 " + user.getUserName() + " 的地市是不能为空");
                    continue;
                }
                 SysRegion region = sysRegionService.selectByRegName(user.getRegName());
                if (Objects.isNull(region)){
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账号 " + user.getUserName() + " 的地市有误");
                    continue;
                }
                user.setRegId(region.getRegId());
                // 验证是否存在这个用户
                SysUser u = userMapper.selectUserByUserName(user.getUserName());
                if (StrUtils.isNull(u)) {
                    BeanValidators.validateWithException(validator, user);
                    user.setPassword(SecurityUtils.encryptPassword(password));
                    user.setCreateBy(operName);
                    this.insertUser(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, user);
                    checkUserAllowed(user);
                    checkUserDataScope(user.getUserId());
                    user.setUpdateBy(operName);
                    this.updateUser(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账号 " + user.getUserName() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + user.getUserName() + " 导入失败；";
                failureMsg.append(msg);
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    @Override
    public SysUser selectUserByPhone(String phone) {
        return userMapper.selectUserByPhone(phone);
    }

    @Override
    @Transactional
    public int insertGuideUser(SysUserVo userVo) {
        SysUser user = new SysUser();
        BeanUtils.copyProperties(userVo,user);
        // 新增用户信息
        int rows = userMapper.insertUser(user);

        // 新增用户与角色管理
        insertUserRole(user);
        userVo.setUserId(user.getUserId());
        insertUserBind(userVo);
        return rows;
    }

    @Override
    @Transactional
    public AjaxResult updateGuideUser(SysUserVo userVo) {
        SysUser user = new SysUser();
        BeanUtils.copyProperties(userVo,user);
        //删除用户和角色关联
        Long userId = user.getUserId();
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 新增用户与角色管理
        insertUserRole(user);

        userMapper.updateUser(user);
        return updateUserBind(userVo);
    }

    /**
     * 忘记密码
     * @param userVo
     * @return
     */
    @Override
    public AjaxResult forget(UserVo userVo) {
        SysUser sysUser = userMapper.selectUserByPhone(userVo.getPhoneNumber());
        if (!sysUser.getUserName().equals(userVo.getUsername())) {
            return AjaxResult.error("用户名和电话不匹配");
        }
        //获取验证码
        String verifyCode = redisCache.getCacheObject(Constants.VERIFY_SMS_CODE + userVo.getPhoneNumber());
        if (StrUtils.isEmpty(verifyCode)){
            return AjaxResult.error("验证码已过期");
        }
        if (!userVo.getCode().equals(verifyCode)){
            return AjaxResult.error("验证码错误");
        }
        //获取密码
        String aPassword = ParamParse.parseStr(userVo.getAgainPassword());
        String newPassword = ParamParse.parseStr(userVo.getNewPassword());
        if (StrUtils.isEmpty(aPassword) || StrUtils.isEmpty(newPassword)){
            return AjaxResult.error("密码不能为空");
        }
        if (!UserConstants.PASSWORD.matcher(newPassword).matches()){
            return AjaxResult.error("用户密码8-20位，由数字、大写字母、小写字母、特殊字符三种组成！");
        }
        if (!aPassword.equals(newPassword)){
            return AjaxResult.error("两次密码不一致");
        }
        //校验用户名与手机号
        String msg = checkPwdNameAndPhone(aPassword, sysUser.getUserName(), sysUser.getPhoneNumber());
        if (StrUtils.isNotNull(msg)){
            return AjaxResult.error(msg);
        }
        String checkPwd = checkPwd(aPassword);
        if (StrUtils.isNotNull(checkPwd)) {
            return AjaxResult.error("密码包含特殊字符[" + checkPwd + "]");
        }
        sysUser.setPassword(SecurityUtils.encryptPassword(aPassword));
        sysUser.setUpdateBy(sysUser.getUserName());
        userMapper.updateUser(sysUser);
        redisCache.deleteObject(Constants.VERIFY_SMS_CODE + userVo.getPhoneNumber());
        return AjaxResult.success();
    }

    @Override
    public AjaxResult updatePwd(UserDataVo userVo) {
        SysUser sysUser = userMapper.selectUserByUserName(userVo.getUsername());
        if (StrUtils.isNull(sysUser)){
            return AjaxResult.error("用户名有误！");
        }
        String aPassword = ParamParse.parseStr(userVo.getAgainPassword());
        String newPassword = ParamParse.parseStr(userVo.getNewPassword());
        if (!UserConstants.PASSWORD.matcher(newPassword).matches()){
            return AjaxResult.error("用户密码8-20位，由数字、大写字母、小写字母、特殊字符三种组成！");
        }
        if (!aPassword.equals(newPassword)){
            return AjaxResult.error("两次密码不一致");
        }
        //校验用户名与手机号
        String msg = checkPwdNameAndPhone(aPassword, sysUser.getUserName(), sysUser.getPhoneNumber());
        if (StrUtils.isNotNull(msg)){
            return AjaxResult.error(msg);
        }
        String checkPwd = checkPwd(aPassword);
        if (StrUtils.isNotNull(checkPwd)) {
            return AjaxResult.error("密码包含特殊字符[" + checkPwd + "]");
        }
        sysUser.setPassword(SecurityUtils.encryptPassword(aPassword));
        sysUser.setUpdateTime(DateTimeUtils.getNowDate());
        sysUser.setUpdateBy(sysUser.getUserName());
        userMapper.updateUser(sysUser);
        //删除缓存
        redisCache.deleteObject(CacheConstants.LOGIN_TOKEN_KEY + userVo.getUsername());
        return AjaxResult.success();
    }


    private void insertUserBind(SysUserVo userVo) {
        Date nowDate = DateTimeUtils.getNowDate();
        if (userVo.getUserBinds().size()>0){
            List<SysUserBind> userBinds = userVo.getUserBinds();
            userBinds.forEach(userBind ->{
                userBind.setUserId(userVo.getUserId());
                userBind.setAccountStatus("1");
                userBind.setBindTime(nowDate);
            });
            userBindMapper.saveBatch(userBinds);
        }
    }
    private AjaxResult updateUserBind(SysUserVo userVo){
        Date nowDate = DateTimeUtils.getNowDate();
        List<SysUserBind> userBinds = userVo.getUserBinds();
        if (!checkUserBind(userBinds)){
            return AjaxResult.success("该系统用户名已被使用");
        }
        for (SysUserBind userBind : userBinds){
            if (Objects.isNull(userBind.getId())){
                userBind.setUserId(userVo.getUserId());
                userBind.setAccountStatus("1");
                userBind.setBindTime(nowDate);
                userBindMapper.insertSysUserBind(userBind);
            }
        }
        return AjaxResult.success();
    }
    /**
     *  添加校验
     */
    private Boolean checkUserBind(List<SysUserBind> userBinds){
        for (SysUserBind userBind : userBinds) {
            List<SysUserBind> users = userBindMapper.selectSysAndUser(userBind);
            if (!CollectionUtils.isEmpty(users)){
                return false;
            }
        }
        return true;
    }
    /**
     * 校验密码是否包含手机号与用户名
     */
    public String checkPwdNameAndPhone(String pwd,String username,String phone) {
        if (pwd.contains(username) || pwd.contains(phone)){
            return "密码不能包含手机号与用户名";
        }
        return null;
    }
    /**
     * 校验密码
     * @param pwd
     * @return
     */
    public String checkPwd(String pwd) {
        List<SysEnumPwd> pwds = pwdMapper.selectSysEnumPwdList(new SysEnumPwd());
        if (pwds.size() > 0) {
            for (SysEnumPwd sysEnumPwd : pwds) {
                if (pwd.contains(sysEnumPwd.getPwd())) {
                    return sysEnumPwd.getPwd();
                }
            }
        }
        return null;
    }
}
