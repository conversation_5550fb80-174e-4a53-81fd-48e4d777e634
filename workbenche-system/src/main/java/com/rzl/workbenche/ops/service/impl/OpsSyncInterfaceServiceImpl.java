package com.rzl.workbenche.ops.service.impl;

import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.ops.domain.OpsSyncInterface;
import com.rzl.workbenche.ops.mapper.OpsSyncInterfaceMapper;
import com.rzl.workbenche.ops.service.IOpsSyncInterfaceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 同步接口管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-24
 */
@Service
public class OpsSyncInterfaceServiceImpl implements IOpsSyncInterfaceService
{
    @Autowired
    private OpsSyncInterfaceMapper opsSyncInterfaceMapper;

    /**
     * 查询同步接口管理
     *
     * @param id 同步接口管理主键
     * @return 同步接口管理
     */
    @Override
    public OpsSyncInterface selectOpsSyncInterfaceById(Long id)
    {
        return opsSyncInterfaceMapper.selectOpsSyncInterfaceById(id);
    }

    /**
     * 查询同步接口管理列表
     *
     * @param opsSyncInterface 同步接口管理
     * @return 同步接口管理
     */
    @Override
    public List<OpsSyncInterface> selectOpsSyncInterfaceList(OpsSyncInterface opsSyncInterface)
    {
        return opsSyncInterfaceMapper.selectOpsSyncInterfaceList(opsSyncInterface);
    }

    /**
     * 新增同步接口管理
     *
     * @param opsSyncInterface 同步接口管理
     * @return 结果
     */
    @Override
    public int insertOpsSyncInterface(OpsSyncInterface opsSyncInterface)
    {
        opsSyncInterface.setDelFlag(0);
        return opsSyncInterfaceMapper.insertOpsSyncInterface(opsSyncInterface);
    }

    /**
     * 修改同步接口管理
     *
     * @param opsSyncInterface 同步接口管理
     * @return 结果
     */
    @Override
    public int updateOpsSyncInterface(OpsSyncInterface opsSyncInterface)
    {
        return opsSyncInterfaceMapper.updateOpsSyncInterface(opsSyncInterface);
    }

    /**
     * 批量删除同步接口管理
     *
     * @param ids 需要删除的同步接口管理主键
     * @return 结果
     */
    @Override
    public int deleteOpsSyncInterfaceByIds(Long[] ids)
    {
        return opsSyncInterfaceMapper.deleteOpsSyncInterfaceByIds(ids);
    }

    /**
     * 删除同步接口管理信息
     *
     * @param id 同步接口管理主键
     * @return 结果
     */
    @Override
    public AjaxResult deleteOpsSyncInterfaceById(Long id)
    {
        OpsSyncInterface opsSyncInterface = opsSyncInterfaceMapper.selectOpsSyncInterfaceById(id);
        if ("1".equals(opsSyncInterface.getStatus())){
            return AjaxResult.error("请禁用后删除");
        }
        opsSyncInterfaceMapper.deleteOpsSyncInterfaceById(id);
        return AjaxResult.success();
    }
}
