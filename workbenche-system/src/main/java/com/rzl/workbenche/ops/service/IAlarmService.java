package com.rzl.workbenche.ops.service;

import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.ops.domain.Alarm;
import com.rzl.workbenche.ops.domain.export.AlarmExport;
import com.rzl.workbenche.ops.domain.vo.DataVo;

import java.util.List;


/**
 * 运维告警管理Service接口
 *
 * <AUTHOR>
 * @date 2023-08-23
 */
public interface IAlarmService
{
    /**
     * 查询运维告警管理
     *
     * @param id 运维告警管理主键
     * @return 运维告警管理
     */
    public Alarm selectAlarmById(Long id);

    /**
     * 查询运维告警管理列表
     *
     * @param alarm 运维告警管理
     * @return 运维告警管理集合
     */
    public List<Alarm> selectAlarmList(Alarm alarm);

    /**
     * 新增运维告警管理
     *
     * @param alarm 运维告警管理
     * @return 结果
     */
    public int insertAlarm(Alarm alarm);

    /**
     * 修改运维告警管理
     *
     * @param alarm 运维告警管理
     * @return 结果
     */
    public int updateAlarm(Alarm alarm);

    /**
     * 批量删除运维告警管理
     *
     * @param ids 需要删除的运维告警管理主键集合
     * @return 结果
     */
    public int deleteAlarmByIds(Long[] ids);

    /**
     * 删除运维告警管理信息
     *
     * @param id 运维告警管理主键
     * @return 结果
     */
    public int deleteAlarmById(Long id);
    /**
     * 处理运维告警管理
     * @param alarm
     * @return
     */
    AjaxResult handle(Alarm alarm) throws Exception;

    /**
     * 告警导出
     * @param alarm
     * @return
     */
    List<AlarmExport> selectAlarmListExport(Alarm alarm);
    /**
     * 批量处理接口
     */
    AjaxResult batchHandle(List<Long> ids, String opinion);

    Long selectCount(Alarm alarm);

    /**
     * 告警采集
     * @param alarms
     * @return
     */
    AjaxResult saveBatchAlarm(DataVo alarms);

    /**
     * 告警状态采集
     * @param dataVo
     * @return
     */
    AjaxResult updateAlarmStatus(DataVo dataVo);
}
