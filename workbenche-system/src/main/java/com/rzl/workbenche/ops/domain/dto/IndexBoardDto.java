package com.rzl.workbenche.ops.domain.dto;

import com.rzl.workbenche.common.core.domain.BaseEntity;
import java.util.List;

/**
 * 指示看板对象 index_board
 *
 * <AUTHOR>
 * @date 2023-08-24
 */
public class IndexBoardDto extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 所属系统 */

    private String system;

    /** 指标名称 */

    private String indexName;

    /** 指标类型 */

    private String indexType;

    /** 指标状态 */

    private String indexStatus;

    /** 是否首页展示 */

    private String isPlay;

    /**
     * 角色ID
     */
    private Long roleId;
    /**
     * 权限
     */
    private List<Long> ids;

    public List<Long> getIds() {
        return ids;
    }

    public void setIds(List<Long> ids) {
        this.ids = ids;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSystem() {
        return system;
    }

    public void setSystem(String system) {
        this.system = system;
    }

    public String getIndexName() {
        return indexName;
    }

    public void setIndexName(String indexName) {
        this.indexName = indexName;
    }

    public String getIndexType() {
        return indexType;
    }

    public void setIndexType(String indexType) {
        this.indexType = indexType;
    }

    public String getIndexStatus() {
        return indexStatus;
    }

    public void setIndexStatus(String indexStatus) {
        this.indexStatus = indexStatus;
    }

    public String getIsPlay() {
        return isPlay;
    }

    public void setIsPlay(String isPlay) {
        this.isPlay = isPlay;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }
}
