package com.rzl.workbenche.ops.domain;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rzl.workbenche.common.annotation.Excel;
import com.rzl.workbenche.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 运维待办管理对象 ops_do
 *
 * <AUTHOR>
 * @date 2023-08-24
 */
public class OpsAgent extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 待办管理id */
    private Long id;

    /** 所属系统 */

    @NotBlank(message = "业务系统不能为空")
    private String system;
    @Excel(name = "所属系统")
    private String systemName;
    /** 待办名称 */
    @Excel(name = "待办名称")
    @NotBlank(message = "待办名称不能为空")
    private String agentName;

    /** 待办类型 */
    @Excel(name = "待办类型")
    @NotBlank(message = "待办类型不能为空")
    private String agentType;

    /** 省份id */
    @Excel(name = "省份id")
    private String prvId;

    /** 省名称 */
    @Excel(name = "省份")
//    @NotBlank(message = "省名称不能为空")
    private String prvName;

    /** 市id */
    @Excel(name = "市id")
    private String pregId;

    /** 市名称 */
    @Excel(name = "地市")
    @NotBlank(message = "市名称不能为空")
    private String pregName;

    /** 区县id */
    @Excel(name = "区县id")
    private String regId;

    /** 区县名称 */
    @Excel(name = "区县")
    @NotBlank(message = "区县名称不能为空")
    private String regName;

    /** 待办内容(富文本或json) */
    @Excel(name = "待办内容(富文本或json)")
    @NotBlank(message = "待办内容不能为空")
    private String content;

    /** 创建人姓名 */
    @Excel(name = "创建人姓名")
    private String createUser;

    /** 当前处理人 */
    @Excel(name = "当前处理人")
    @NotBlank(message = "当前处理人不能为空")
    private String curtUser;

    /** 处理状态 */
    @Excel(name = "处理状态")
    private String handleStatus;

    /** 紧急程度 */
    @Excel(name = "紧急程度")
    private String urgency;

    /** 是否超期 */
    @Excel(name = "是否超期")
    private String isOverdue;

    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处理时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date handleTime;

    /** 处理耗时 */
    @Excel(name = "处理耗时")
    private Long duration;

    /** 处理意见 */
    @Excel(name = "处理意见")
    private String opinion;

    /** 处理人 */
    @Excel(name = "处理人")
    private String handleUser;

    /** 处理耗时(格式转换) */
    @Excel(name = "处理耗时")
    private String dura;

    private String agentCode;

    /** 计划时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "创建时间不能为空")
    private Date createTime;
    /** 处理耗时 */
    private String agentUser;

    /** 处理耗时 */
    private String agentPhone;

    private String agentAddress;

    private List<String> regIds;

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    public List<String> getRegIds() {
        return regIds;
    }

    public void setRegIds(List<String> regIds) {
        this.regIds = regIds;
    }

    public String getAgentCode() {
        return agentCode;
    }

    public void setAgentCode(String agentCode) {
        this.agentCode = agentCode;
    }

    public String getDura() {
        return dura;
    }

    public void setDura(String dura) {
        this.dura = dura;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setSystem(String system)
    {
        this.system = system;
    }

    public String getSystem()
    {
        return system;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getAgentType() {
        return agentType;
    }

    public void setAgentType(String agentType) {
        this.agentType = agentType;
    }

    public void setPrvId(String prvId)
    {
        this.prvId = prvId;
    }

    public String getPrvId()
    {
        return prvId;
    }

    public String getPrvName() {
        return prvName;
    }

    public void setPrvName(String prvName) {
        this.prvName = prvName;
    }

    public void setPregId(String pregId)
    {
        this.pregId = pregId;
    }

    public String getPregId()
    {
        return pregId;
    }
    public void setPregName(String pregName)
    {
        this.pregName = pregName;
    }

    public String getPregName()
    {
        return pregName;
    }
    public void setRegId(String regId)
    {
        this.regId = regId;
    }

    public String getRegId()
    {
        return regId;
    }
    public void setRegName(String regName)
    {
        this.regName = regName;
    }

    public String getRegName()
    {
        return regName;
    }
    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }
    public void setCreateUser(String createUser)
    {
        this.createUser = createUser;
    }

    public String getCreateUser()
    {
        return createUser;
    }

    public String getCurtUser() {
        return curtUser;
    }

    public void setCurtUser(String curtUser) {
        this.curtUser = curtUser;
    }

    public void setHandleStatus(String handleStatus)
    {
        this.handleStatus = handleStatus;
    }

    public String getHandleStatus()
    {
        return handleStatus;
    }
    public void setUrgency(String urgency)
    {
        this.urgency = urgency;
    }

    public String getUrgency()
    {
        return urgency;
    }
    public void setIsOverdue(String isOverdue)
    {
        this.isOverdue = isOverdue;
    }

    public String getIsOverdue()
    {
        return isOverdue;
    }
    public void setHandleTime(Date handleTime)
    {
        this.handleTime = handleTime;
    }

    public Date getHandleTime()
    {
        return handleTime;
    }
    public void setDuration(Long duration)
    {
        this.duration = duration;
    }

    public Long getDuration()
    {
        return duration;
    }
    public void setOpinion(String opinion)
    {
        this.opinion = opinion;
    }

    public String getOpinion()
    {
        return opinion;
    }
    public void setHandleUser(String handleUser)
    {
        this.handleUser = handleUser;
    }

    public String getHandleUser()
    {
        return handleUser;
    }

    public Date getPlanTime() {
        return planTime;
    }

    public void setPlanTime(Date planTime) {
        this.planTime = planTime;
    }

    public String getAgentUser() {
        return agentUser;
    }

    public void setAgentUser(String agentUser) {
        this.agentUser = agentUser;
    }

    public String getAgentPhone() {
        return agentPhone;
    }

    public void setAgentPhone(String agentPhone) {
        this.agentPhone = agentPhone;
    }

    public String getAgentAddress() {
        return agentAddress;
    }

    public void setAgentAddress(String agentAddress) {
        this.agentAddress = agentAddress;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("system", getSystem())
                .append("agentName", getAgentName())
                .append("agentType", getAgentType())
                .append("prvId", getPrvId())
                .append("prvName", getPrvName())
                .append("pregId", getPregId())
                .append("pregName", getPregName())
                .append("regId", getRegId())
                .append("regName", getRegName())
                .append("content", getContent())
                .append("createTime", getCreateTime())
                .append("createUser", getCreateUser())
                .append("curtUser", getCurtUser())
                .append("handleStatus", getHandleStatus())
                .append("urgency", getUrgency())
                .append("isOverdue", getIsOverdue())
                .append("handleTime", getHandleTime())
                .append("duration", getDuration())
                .append("opinion", getOpinion())
                .append("handleUser", getHandleUser())
                .toString();
    }

}
