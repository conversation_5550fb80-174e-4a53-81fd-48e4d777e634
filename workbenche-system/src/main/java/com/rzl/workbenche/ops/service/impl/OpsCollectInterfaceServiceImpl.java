package com.rzl.workbenche.ops.service.impl;

import java.util.List;

import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.ops.domain.OpsCollectInterface;
import com.rzl.workbenche.ops.mapper.OpsCollectInterfaceMapper;
import com.rzl.workbenche.ops.service.IOpsCollectInterfaceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 采集接口管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-24
 */
@Service
public class OpsCollectInterfaceServiceImpl implements IOpsCollectInterfaceService
{
    @Autowired
    private OpsCollectInterfaceMapper opsCollectInterfaceMapper;

    /**
     * 查询采集接口管理
     *
     * @param id 采集接口管理主键
     * @return 采集接口管理
     */
    @Override
    public OpsCollectInterface selectOpsCollectInterfaceById(Long id)
    {
        return opsCollectInterfaceMapper.selectOpsCollectInterfaceById(id);
    }

    /**
     * 查询采集接口管理列表
     *
     * @param opsCollectInterface 采集接口管理
     * @return 采集接口管理
     */
    @Override
    public List<OpsCollectInterface> selectOpsCollectInterfaceList(OpsCollectInterface opsCollectInterface)
    {
        return opsCollectInterfaceMapper.selectOpsCollectInterfaceList(opsCollectInterface);
    }

    /**
     * 新增采集接口管理
     *
     * @param opsCollectInterface 采集接口管理
     * @return 结果
     */
    @Override
    public int insertOpsCollectInterface(OpsCollectInterface opsCollectInterface)
    {
        opsCollectInterface.setDelFlag(0);
        return opsCollectInterfaceMapper.insertOpsCollectInterface(opsCollectInterface);
    }

    /**
     * 修改采集接口管理
     *
     * @param opsCollectInterface 采集接口管理
     * @return 结果
     */
    @Override
    public int updateOpsCollectInterface(OpsCollectInterface opsCollectInterface)
    {
        return opsCollectInterfaceMapper.updateOpsCollectInterface(opsCollectInterface);
    }

    /**
     * 批量删除采集接口管理
     *
     * @param ids 需要删除的采集接口管理主键
     * @return 结果
     */
    @Override
    public int deleteOpsCollectInterfaceByIds(Long[] ids)
    {
        return opsCollectInterfaceMapper.deleteOpsCollectInterfaceByIds(ids);
    }

    /**
     * 删除采集接口管理信息
     *
     * @param id 采集接口管理主键
     * @return 结果
     */
    @Override
    public AjaxResult deleteOpsCollectInterfaceById(Long id)
    {
        OpsCollectInterface opsCollectInterface = opsCollectInterfaceMapper.selectOpsCollectInterfaceById(id);
        if ("1".equals(opsCollectInterface.getStatus())){
            return AjaxResult.error("请禁用后删除");
        }
        opsCollectInterfaceMapper.deleteOpsCollectInterfaceById(id);
        return AjaxResult.success();
    }
}
