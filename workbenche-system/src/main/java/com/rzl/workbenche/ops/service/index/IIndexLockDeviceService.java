package com.rzl.workbenche.ops.service.index;

import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.ops.domain.index.IndexLockDevice;
import com.rzl.workbenche.ops.domain.vo.DataVo;

import java.util.List;

/**
 * 智能锁设施指标Service接口
 * 
 * <AUTHOR>
 * @date 2023-09-10
 */
public interface IIndexLockDeviceService 
{
    /**
     * 查询智能锁设施指标
     * 
     * @param id 智能锁设施指标主键
     * @return 智能锁设施指标
     */
    public IndexLockDevice selectIndexLockDeviceById(Long id);

    /**
     * 查询智能锁设施指标列表
     * 
     * @param indexLockDevice 智能锁设施指标
     * @return 智能锁设施指标集合
     */
    public List<IndexLockDevice> selectIndexLockDeviceList(IndexLockDevice indexLockDevice);

    /**
     * 新增智能锁设施指标
     * 
     * @param indexLockDevice 智能锁设施指标
     * @return 结果
     */
    public int insertIndexLockDevice(IndexLockDevice indexLockDevice);

    /**
     * 修改智能锁设施指标
     * 
     * @param indexLockDevice 智能锁设施指标
     * @return 结果
     */
    public int updateIndexLockDevice(IndexLockDevice indexLockDevice);

    /**
     * 批量删除智能锁设施指标
     * 
     * @param ids 需要删除的智能锁设施指标主键集合
     * @return 结果
     */
    public int deleteIndexLockDeviceByIds(Long[] ids);

    /**
     * 删除智能锁设施指标信息
     * 
     * @param id 智能锁设施指标主键
     * @return 结果
     */
    public int deleteIndexLockDeviceById(Long id);

    AjaxResult saveBatchIndexLockDevice(DataVo dataVo);
}
