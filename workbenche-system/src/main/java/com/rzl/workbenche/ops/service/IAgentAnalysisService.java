package com.rzl.workbenche.ops.service;

import com.rzl.workbenche.ops.domain.AgentAnalysis;

import java.util.List;


/**
 * 待办效率分析Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
public interface IAgentAnalysisService
{
    /**
     * 查询待办效率分析
     * 
     * @param id 待办效率分析主键
     * @return 待办效率分析
     */
    public AgentAnalysis selectAgentAnalysisById(Long id);

    /**
     * 查询待办效率分析列表
     * 
     * @param agentAnalysis 待办效率分析
     * @return 待办效率分析集合
     */
    public List<AgentAnalysis> selectAgentAnalysisList(AgentAnalysis agentAnalysis);

    /**
     * 新增待办效率分析
     * 
     * @param agentAnalysis 待办效率分析
     * @return 结果
     */
    public int insertAgentAnalysis(AgentAnalysis agentAnalysis);

    /**
     * 修改待办效率分析
     * 
     * @param agentAnalysis 待办效率分析
     * @return 结果
     */
    public int updateAgentAnalysis(AgentAnalysis agentAnalysis);

    /**
     * 批量删除待办效率分析
     * 
     * @param ids 需要删除的待办效率分析主键集合
     * @return 结果
     */
    public int deleteAgentAnalysisByIds(Long[] ids);

    /**
     * 删除待办效率分析信息
     * 
     * @param id 待办效率分析主键
     * @return 结果
     */
    public int deleteAgentAnalysisById(Long id);
}
