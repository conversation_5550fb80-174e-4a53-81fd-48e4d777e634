package com.rzl.workbenche.ops.service.impl;

import java.util.List;
import com.rzl.workbenche.common.utils.DateTimeUtils;
import com.rzl.workbenche.ops.domain.SystemAnalysis;
import com.rzl.workbenche.ops.mapper.SystemAnalysisMapper;
import com.rzl.workbenche.ops.service.ISystemAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 系统情况分析Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-24
 */
@Service
public class SystemAnalysisServiceImpl implements ISystemAnalysisService
{
    @Autowired
    private SystemAnalysisMapper systemAnalysisMapper;

    /**
     * 查询系统情况分析
     *
     * @param id 系统情况分析主键
     * @return 系统情况分析
     */
    @Override
    public SystemAnalysis selectSystemAnalysisById(Long id)
    {
        return systemAnalysisMapper.selectSystemAnalysisById(id);
    }

    /**
     * 查询系统情况分析列表
     *
     * @param systemAnalysis 系统情况分析
     * @return 系统情况分析
     */
    @Override
    public List<SystemAnalysis> selectSystemAnalysisList(SystemAnalysis systemAnalysis)
    {
        return systemAnalysisMapper.selectSystemAnalysisList(systemAnalysis);
    }

    /**
     * 新增系统情况分析
     *
     * @param systemAnalysis 系统情况分析
     * @return 结果
     */
    @Override
    public int insertSystemAnalysis(SystemAnalysis systemAnalysis)
    {
        systemAnalysis.setUpdateTime(DateTimeUtils.getNowDate());
        return systemAnalysisMapper.insertSystemAnalysis(systemAnalysis);
    }

    /**
     * 修改系统情况分析
     *
     * @param systemAnalysis 系统情况分析
     * @return 结果
     */
    @Override
    public int updateSystemAnalysis(SystemAnalysis systemAnalysis)
    {
        systemAnalysis.setUpdateTime(DateTimeUtils.getNowDate());
        return systemAnalysisMapper.updateSystemAnalysis(systemAnalysis);
    }

    /**
     * 批量删除系统情况分析
     *
     * @param ids 需要删除的系统情况分析主键
     * @return 结果
     */
    @Override
    public int deleteSystemAnalysisByIds(Long[] ids)
    {
        return systemAnalysisMapper.deleteSystemAnalysisByIds(ids);
    }

    /**
     * 删除系统情况分析信息
     *
     * @param id 系统情况分析主键
     * @return 结果
     */
    @Override
    public int deleteSystemAnalysisById(Long id)
    {
        return systemAnalysisMapper.deleteSystemAnalysisById(id);
    }
}
