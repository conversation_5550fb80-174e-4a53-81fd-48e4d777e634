package com.rzl.workbenche.ops.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rzl.workbenche.common.annotation.Excel;
import com.rzl.workbenche.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 无感接入系统向导配置对象 ops_system_guide
 *
 * <AUTHOR>
 * @date 2023-11-09
 */
public class OpsSystemGuide extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 系统名称 */
    @Excel(name = "系统名称")
    private String systemName;

    /** 系统编码 */
    @Excel(name = "系统编码")
    private String systemCode;


    /** 系统标识 */
    @Excel(name = "系统标识")
    @NotBlank(message = "系统标识不能为空")
    private String systemMark;

    /** 登录地址 */
    @Excel(name = "登录地址")
    @NotBlank(message = "登录地址不能为空")
    private String loginUrl;

    /**
     * 0：未接入  1：已接入
     */
    private String status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间",width = 30,dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setSystemCode(String systemCode)
    {
        this.systemCode = systemCode;
    }

    public String getSystemCode()
    {
        return systemCode;
    }
    public void setSystemName(String systemName)
    {
        this.systemName = systemName;
    }

    public String getSystemName()
    {
        return systemName;
    }
    public void setSystemMark(String systemMark)
    {
        this.systemMark = systemMark;
    }

    public String getSystemMark()
    {
        return systemMark;
    }
    public void setLoginUrl(String loginUrl)
    {
        this.loginUrl = loginUrl;
    }

    public String getLoginUrl()
    {
        return loginUrl;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("systemCode", getSystemCode())
            .append("systemName", getSystemName())
            .append("systemMark", getSystemMark())
            .append("createTime", getCreateTime())
            .append("loginUrl", getLoginUrl())
            .toString();
    }
}
