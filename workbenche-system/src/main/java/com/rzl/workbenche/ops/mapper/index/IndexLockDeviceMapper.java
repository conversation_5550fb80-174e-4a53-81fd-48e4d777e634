package com.rzl.workbenche.ops.mapper.index;

import com.rzl.workbenche.ops.domain.index.IndexLockDevice;
import com.rzl.workbenche.ops.domain.vo.CardStatisticsVo;

import java.util.List;

/**
 * 智能锁设施指标Mapper接口
 *
 * <AUTHOR>
 * @date 2023-09-10
 */
public interface IndexLockDeviceMapper
{
    /**
     * 查询智能锁设施指标
     *
     * @param id 智能锁设施指标主键
     * @return 智能锁设施指标
     */
    public IndexLockDevice selectIndexLockDeviceById(Long id);

    /**
     * 查询智能锁设施指标列表
     *
     * @param indexLockDevice 智能锁设施指标
     * @return 智能锁设施指标集合
     */
    public List<IndexLockDevice> selectIndexLockDeviceList(IndexLockDevice indexLockDevice);

    /**
     * 新增智能锁设施指标
     *
     * @param indexLockDevice 智能锁设施指标
     * @return 结果
     */
    public int insertIndexLockDevice(IndexLockDevice indexLockDevice);

    /**
     * 修改智能锁设施指标
     *
     * @param indexLockDevice 智能锁设施指标
     * @return 结果
     */
    public int updateIndexLockDevice(IndexLockDevice indexLockDevice);

    /**
     * 删除智能锁设施指标
     *
     * @param id 智能锁设施指标主键
     * @return 结果
     */
    public int deleteIndexLockDeviceById(Long id);

    /**
     * 批量删除智能锁设施指标
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteIndexLockDeviceByIds(Long[] ids);

    void saveBatchIndexLockDevice(List<IndexLockDevice> devices);

    CardStatisticsVo selectStatistics();
}
