package com.rzl.workbenche.ops.service;

import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.ops.domain.IndexBoard;
import com.rzl.workbenche.ops.domain.dto.IndexBoardDto;
import com.rzl.workbenche.ops.domain.vo.IndexBoardVo;

import java.util.List;


/**
 * 指示看板Service接口
 *
 * <AUTHOR>
 * @date 2023-08-24
 */
public interface IIndexBoardService
{
    /**
     * 查询指示看板
     *
     * @param id 指示看板主键
     * @return 指示看板
     */
    public IndexBoard selectIndexBoardById(Long id);

    /**
     * 查询指示看板列表
     *
     * @param indexBoard 指示看板
     * @return 指示看板集合
     */
    public List<IndexBoard> selectIndexBoardList(IndexBoard indexBoard);

    /**
     * 新增指示看板
     *
     * @param indexBoard 指示看板
     * @return 结果
     */
    public int insertIndexBoard(IndexBoard indexBoard);

    /**
     * 修改指示看板
     *
     * @param indexBoard 指示看板
     * @return 结果
     */
    public int updateIndexBoard(IndexBoard indexBoard);

    /**
     * 批量删除指示看板
     *
     * @param ids 需要删除的指示看板主键集合
     * @return 结果
     */
    public int deleteIndexBoardByIds(Long[] ids);

    /**
     * 删除指示看板信息
     *
     * @param id 指示看板主键
     * @return 结果
     */
    public int deleteIndexBoardById(Long id);

    List<IndexBoardVo> selectIndexBoardListVo(IndexBoard indexBoard);

    /**
     * 工作台图标展示
     * @param indexBoard
     * @return
     */
    List<IndexBoardVo> selectIndexBoardListCharsVo(IndexBoard indexBoard);
    /**
     * 查询已指标角色列表
     */
    List<IndexBoard> selectIndexAllocatedList(IndexBoardDto indexBoardDto);
    /**
     * 查询未分配指标角色列表
     */
    List<IndexBoard> selectIndexUnallocatedList(IndexBoardDto indexBoardDto);
    /**
     * 获取指示看板树
     */
    AjaxResult getTree();
}
