package com.rzl.workbenche.ops.mapper;

import com.rzl.workbenche.ops.domain.OpsSyncInterface;

import java.util.List;


/**
 * 同步接口管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
public interface OpsSyncInterfaceMapper 
{
    /**
     * 查询同步接口管理
     * 
     * @param id 同步接口管理主键
     * @return 同步接口管理
     */
    public OpsSyncInterface selectOpsSyncInterfaceById(Long id);

    /**
     * 查询同步接口管理列表
     * 
     * @param opsSyncInterface 同步接口管理
     * @return 同步接口管理集合
     */
    public List<OpsSyncInterface> selectOpsSyncInterfaceList(OpsSyncInterface opsSyncInterface);

    /**
     * 新增同步接口管理
     * 
     * @param opsSyncInterface 同步接口管理
     * @return 结果
     */
    public int insertOpsSyncInterface(OpsSyncInterface opsSyncInterface);

    /**
     * 修改同步接口管理
     * 
     * @param opsSyncInterface 同步接口管理
     * @return 结果
     */
    public int updateOpsSyncInterface(OpsSyncInterface opsSyncInterface);

    /**
     * 删除同步接口管理
     * 
     * @param id 同步接口管理主键
     * @return 结果
     */
    public int deleteOpsSyncInterfaceById(Long id);

    /**
     * 批量删除同步接口管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOpsSyncInterfaceByIds(Long[] ids);
}
