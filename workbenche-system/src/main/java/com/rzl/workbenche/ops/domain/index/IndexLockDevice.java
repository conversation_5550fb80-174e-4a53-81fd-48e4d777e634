package com.rzl.workbenche.ops.domain.index;

import com.rzl.workbenche.common.annotation.Excel;
import com.rzl.workbenche.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;

/**
 * 智能锁设施指标对象 index_lock_device
 *
 * <AUTHOR>
 * @date 2023-09-10
 */
public class IndexLockDevice extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 所属系统 */
    @Excel(name = "所属系统")
    @NotBlank(message = "业务系统不能为空")
    private String system;

    /** 省份id */
    @Excel(name = "省份id")
    private String prvId;

    /** 省名称 */
    @Excel(name = "省名称")
//    @NotBlank(message = "省名称不能为空")
    private String prvName;

    /** 市id */
    @Excel(name = "市id")
    private String pregId;

    /** 地市 */
    @Excel(name = "地市")
    @NotBlank(message = "地市不能为空")
    private String pregName;

    /** 区县id */
    @Excel(name = "区县id")
    private String regId;

    /** 区县名称 */
    @Excel(name = "区县名称")
    @NotBlank(message = "区县名称不能为空")
    private String regName;

    /** 设施名称 */
    @Excel(name = "设施名称")
    @NotBlank(message = "设施名称不能为空")
    private String deviceName;

    /** 设施类型 */
    @Excel(name = "设施类型")
    @NotBlank(message = "设施类型不能为空")
    private String deviceType;

    /** 详细地址 */
    @Excel(name = "详细地址")
    @NotBlank(message = "详细地址不能为空")
    private String deviceAddress;

    /** 添加时间 */
    @Excel(name = "添加时间")
    @NotBlank(message = "添加时间不能为空")
    private String addTime;

    /** 锁数量 */
    @Excel(name = "锁数量")
    @NotBlank(message = "锁数量不能为空")
    private String lockNum;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setSystem(String system)
    {
        this.system = system;
    }

    public String getSystem()
    {
        return system;
    }
    public void setPrvId(String prvId)
    {
        this.prvId = prvId;
    }

    public String getPrvId()
    {
        return prvId;
    }
    public void setPrvName(String prvName)
    {
        this.prvName = prvName;
    }

    public String getPrvName()
    {
        return prvName;
    }
    public void setPregId(String pregId)
    {
        this.pregId = pregId;
    }

    public String getPregId()
    {
        return pregId;
    }
    public void setPregName(String pregName)
    {
        this.pregName = pregName;
    }

    public String getPregName()
    {
        return pregName;
    }
    public void setRegId(String regId)
    {
        this.regId = regId;
    }

    public String getRegId()
    {
        return regId;
    }
    public void setRegName(String regName)
    {
        this.regName = regName;
    }

    public String getRegName()
    {
        return regName;
    }
    public void setDeviceName(String deviceName)
    {
        this.deviceName = deviceName;
    }

    public String getDeviceName()
    {
        return deviceName;
    }
    public void setDeviceType(String deviceType)
    {
        this.deviceType = deviceType;
    }

    public String getDeviceType()
    {
        return deviceType;
    }
    public void setDeviceAddress(String deviceAddress)
    {
        this.deviceAddress = deviceAddress;
    }

    public String getDeviceAddress()
    {
        return deviceAddress;
    }
    public void setAddTime(String addTime)
    {
        this.addTime = addTime;
    }

    public String getAddTime()
    {
        return addTime;
    }
    public void setLockNum(String lockNum)
    {
        this.lockNum = lockNum;
    }

    public String getLockNum()
    {
        return lockNum;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("system", getSystem())
            .append("prvId", getPrvId())
            .append("prvName", getPrvName())
            .append("pregId", getPregId())
            .append("pregName", getPregName())
            .append("regId", getRegId())
            .append("regName", getRegName())
            .append("deviceName", getDeviceName())
            .append("deviceType", getDeviceType())
            .append("deviceAddress", getDeviceAddress())
            .append("addTime", getAddTime())
            .append("lockNum", getLockNum())
            .toString();
    }
}
