package com.rzl.workbenche.ops.service.index.impl;

import java.util.List;

import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.utils.ListUtil;
import com.rzl.workbenche.common.utils.ParamParse;
import com.rzl.workbenche.common.utils.StrUtils;
import com.rzl.workbenche.common.utils.ValidationUtils;
import com.rzl.workbenche.ops.domain.index.IndexLock;
import com.rzl.workbenche.ops.domain.vo.DataVo;
import com.rzl.workbenche.ops.mapper.index.IndexLockMapper;
import com.rzl.workbenche.ops.service.index.IIndexLockService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 智能锁指标Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-10
 */
@Service
public class IndexLockServiceImpl implements IIndexLockService
{
    @Autowired
    private IndexLockMapper indexLockMapper;

    /**
     * 查询智能锁指标
     *
     * @param id 智能锁指标主键
     * @return 智能锁指标
     */
    @Override
    public IndexLock selectIndexLockById(Long id)
    {
        return indexLockMapper.selectIndexLockById(id);
    }

    /**
     * 查询智能锁指标列表
     *
     * @param indexLock 智能锁指标
     * @return 智能锁指标
     */
    @Override
    public List<IndexLock> selectIndexLockList(IndexLock indexLock)
    {
        return indexLockMapper.selectIndexLockList(indexLock);
    }

    /**
     * 新增智能锁指标
     *
     * @param indexLock 智能锁指标
     * @return 结果
     */
    @Override
    public int insertIndexLock(IndexLock indexLock)
    {
        return indexLockMapper.insertIndexLock(indexLock);
    }

    /**
     * 修改智能锁指标
     *
     * @param indexLock 智能锁指标
     * @return 结果
     */
    @Override
    public int updateIndexLock(IndexLock indexLock)
    {
        return indexLockMapper.updateIndexLock(indexLock);
    }

    /**
     * 批量删除智能锁指标
     *
     * @param ids 需要删除的智能锁指标主键
     * @return 结果
     */
    @Override
    public int deleteIndexLockByIds(Long[] ids)
    {
        return indexLockMapper.deleteIndexLockByIds(ids);
    }

    /**
     * 删除智能锁指标信息
     *
     * @param id 智能锁指标主键
     * @return 结果
     */
    @Override
    public int deleteIndexLockById(Long id)
    {
        return indexLockMapper.deleteIndexLockById(id);
    }

    @Override
    public AjaxResult saveBatchIndexLock(DataVo dataVo) {
        List<IndexLock> indexLocks = ParamParse.parseClass(dataVo.getData(), IndexLock.class);
        for (IndexLock indexLock : indexLocks) {
            String msg = ValidationUtils.validateObject(indexLock);
            if (StrUtils.isNotNull(msg)){
                return AjaxResult.error(msg);
            }
        }
        List<List<IndexLock>> convert = ListUtil.convert(indexLocks);
        convert.forEach(locks->indexLockMapper.saveBatchIndexLock(locks));
        return AjaxResult.success();
    }
}
