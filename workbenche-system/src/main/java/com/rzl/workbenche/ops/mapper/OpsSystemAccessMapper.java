package com.rzl.workbenche.ops.mapper;

import java.util.List;
import com.rzl.workbenche.ops.domain.OpsSystemAccess;

/**
 * 系统接入Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-10-18
 */
public interface OpsSystemAccessMapper 
{
    /**
     * 查询系统接入
     * 
     * @param id 系统接入主键
     * @return 系统接入
     */
    public OpsSystemAccess selectOpsSystemAccessById(Long id);

    /**
     * 查询系统接入列表
     * 
     * @param opsSystemAccess 系统接入
     * @return 系统接入集合
     */
    public List<OpsSystemAccess> selectOpsSystemAccessList(OpsSystemAccess opsSystemAccess);

    /**
     * 新增系统接入
     * 
     * @param opsSystemAccess 系统接入
     * @return 结果
     */
    public int insertOpsSystemAccess(OpsSystemAccess opsSystemAccess);

    /**
     * 修改系统接入
     * 
     * @param opsSystemAccess 系统接入
     * @return 结果
     */
    public int updateOpsSystemAccess(OpsSystemAccess opsSystemAccess);

    /**
     * 删除系统接入
     * 
     * @param id 系统接入主键
     * @return 结果
     */
    public int deleteOpsSystemAccessById(Long id);

    /**
     * 批量删除系统接入
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOpsSystemAccessByIds(Long[] ids);

    OpsSystemAccess selctBySystem(String system);

    OpsSystemAccess selctBySystemMark(String systemMark);
}