package com.rzl.workbenche.ops.domain.vo;

import com.rzl.workbenche.common.annotation.Excel;

public class IndexBoardVo {
    /** id */
    private Long id;

    /** 所属系统 */
    @Excel(name = "所属系统")
    private String system;

    /** 指标名称 */
    @Excel(name = "指标名称")
    private String indexName;

    /** 指标类型 */
    @Excel(name = "指标类型")
    private String indexType;

    /** 指标状态 */
    @Excel(name = "指标状态")
    private String indexStatus;

    /** 是否首页展示 */
    @Excel(name = "是否首页展示")
    private String isPlay;

    /** 指标顺序 */
    @Excel(name = "指标顺序")
    private Long indexOrder;
    /**
     * 数量
     */
    private String number;

    /**
     * 单位
     * @return
     */
    private String unit;

    private Object data;
    private String oldName;

    public String getOldName() {
        return oldName;
    }

    public void setOldName(String oldName) {
        this.oldName = oldName;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSystem() {
        return system;
    }

    public void setSystem(String system) {
        this.system = system;
    }

    public String getIndexName() {
        return indexName;
    }

    public void setIndexName(String indexName) {
        this.indexName = indexName;
    }

    public String getIndexType() {
        return indexType;
    }

    public void setIndexType(String indexType) {
        this.indexType = indexType;
    }

    public String getIndexStatus() {
        return indexStatus;
    }

    public void setIndexStatus(String indexStatus) {
        this.indexStatus = indexStatus;
    }

    public String getIsPlay() {
        return isPlay;
    }

    public void setIsPlay(String isPlay) {
        this.isPlay = isPlay;
    }

    public Long getIndexOrder() {
        return indexOrder;
    }

    public void setIndexOrder(Long indexOrder) {
        this.indexOrder = indexOrder;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }
}
