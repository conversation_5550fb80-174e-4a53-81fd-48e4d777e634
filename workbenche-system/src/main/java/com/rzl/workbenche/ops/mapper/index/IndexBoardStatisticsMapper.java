package com.rzl.workbenche.ops.mapper.index;

import com.rzl.workbenche.ops.domain.index.IndexBoardStatistics;
import com.rzl.workbenche.ops.domain.index.vo.FiberQualityAnalysisVo;
import com.rzl.workbenche.ops.domain.index.vo.FiberRateVo;
import com.rzl.workbenche.ops.domain.index.vo.QualityAnalysisVo;

import java.util.List;
import java.util.Map;


/**
 * 指示看板统计Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-28
 */
public interface IndexBoardStatisticsMapper
{
    /**
     * 查询指示看板统计
     *
     * @param id 指示看板统计主键
     * @return 指示看板统计
     */
    public IndexBoardStatistics selectIndexBoardStatisticsById(Long id);

    /**
     * 查询指示看板统计列表
     *
     * @param indexBoardStatistics 指示看板统计
     * @return 指示看板统计集合
     */
    public List<IndexBoardStatistics> selectIndexBoardStatisticsList(IndexBoardStatistics indexBoardStatistics);

    /**
     * 新增指示看板统计
     *
     * @param indexBoardStatistics 指示看板统计
     * @return 结果
     */
    public int insertIndexBoardStatistics(IndexBoardStatistics indexBoardStatistics);

    /**
     * 修改指示看板统计
     *
     * @param indexBoardStatistics 指示看板统计
     * @return 结果
     */
    public int updateIndexBoardStatistics(IndexBoardStatistics indexBoardStatistics);

    /**
     * 删除指示看板统计
     *
     * @param id 指示看板统计主键
     * @return 结果
     */
    public int deleteIndexBoardStatisticsById(Long id);

    /**
     * 批量删除指示看板统计
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteIndexBoardStatisticsByIds(Long[] ids);

    /**
     * 统计光缆段数量
     * @return
     */
    Long countNum();

    Map<String, Object> statistics();

    /**
     * 统计光缆质量分析
     * @return
     */
    QualityAnalysisVo cardStatistics();
    /**
     * 合格率统计
     * @return
     */
    List<FiberRateVo> fiberRateStatistics();

    /**
     * 光缆纤芯质量分析
     */
    FiberQualityAnalysisVo fiberQuality();
    /**
     * 光缆段数量
     */
    List<FiberRateVo> numStatistics();
    /**
     * 光缆芯万公里
     */
    List<FiberRateVo> fiberLenth();
    /**
     * 皮长公里
     */
    List<FiberRateVo> fiberSkinLenth();
    /**
     * 统计卡片合格率
     */
    Double selectPassRate();

    void saveBatchBoardStatistics(List<IndexBoardStatistics> statisticsList);
}
