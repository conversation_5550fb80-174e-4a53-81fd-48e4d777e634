package com.rzl.workbenche.ops.service;

import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.ops.domain.OpsCollectInterface;

import java.util.List;


/**
 * 采集接口管理Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
public interface IOpsCollectInterfaceService 
{
    /**
     * 查询采集接口管理
     * 
     * @param id 采集接口管理主键
     * @return 采集接口管理
     */
    public OpsCollectInterface selectOpsCollectInterfaceById(Long id);

    /**
     * 查询采集接口管理列表
     * 
     * @param opsCollectInterface 采集接口管理
     * @return 采集接口管理集合
     */
    public List<OpsCollectInterface> selectOpsCollectInterfaceList(OpsCollectInterface opsCollectInterface);

    /**
     * 新增采集接口管理
     * 
     * @param opsCollectInterface 采集接口管理
     * @return 结果
     */
    public int insertOpsCollectInterface(OpsCollectInterface opsCollectInterface);

    /**
     * 修改采集接口管理
     * 
     * @param opsCollectInterface 采集接口管理
     * @return 结果
     */
    public int updateOpsCollectInterface(OpsCollectInterface opsCollectInterface);

    /**
     * 批量删除采集接口管理
     * 
     * @param ids 需要删除的采集接口管理主键集合
     * @return 结果
     */
    public int deleteOpsCollectInterfaceByIds(Long[] ids);

    /**
     * 删除采集接口管理信息
     * 
     * @param id 采集接口管理主键
     * @return 结果
     */
    public AjaxResult deleteOpsCollectInterfaceById(Long id);
}
