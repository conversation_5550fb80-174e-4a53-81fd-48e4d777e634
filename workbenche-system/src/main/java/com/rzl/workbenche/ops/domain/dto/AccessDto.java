package com.rzl.workbenche.ops.domain.dto;
import com.rzl.workbenche.common.core.domain.BaseEntity;
import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.List;

/**
 * 系统接入对象 ops_system_access
 *
 * <AUTHOR>
 * @date 2023-10-18
 */
public class AccessDto extends BaseEntity
{
    private static final long serialVersionUID = 2L;

    /** id */
    private Long id;
    /** 系统编码 */
    @NotBlank(message = "系统编码不能为空")
    private String systemCode;

    /** 系统名称 */
    @NotBlank(message = "系统名称不能为空")
    private String systemName;

    /** 系统地址 */
    @NotBlank(message = "登录地址不能为空")
    private String loginUrl;

    /** 系统业务 */
    private String systemBusiness;
    @NotBlank(message = "系统标识不能为空")
    private String systemMark;

    public String getSystemMark() {
        return systemMark;
    }

    public void setSystemMark(String systemMark) {
        this.systemMark = systemMark;
    }

    private List<String> sysBusines = new ArrayList<>();

    public List<String> getSysBusines() {
        return sysBusines;
    }

    public void setSysBusines(List<String> sysBusines) {
        this.sysBusines = sysBusines;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setSystemCode(String systemCode)
    {
        this.systemCode = systemCode;
    }

    public String getSystemCode()
    {
        return systemCode;
    }
    public void setSystemName(String systemName)
    {
        this.systemName = systemName;
    }

    public String getSystemName()
    {
        return systemName;
    }


    public String getLoginUrl() {
        return loginUrl;
    }

    public void setLoginUrl(String loginUrl) {
        this.loginUrl = loginUrl;
    }

    public String getSystemBusiness() {
        return systemBusiness;
    }

    public void setSystemBusiness(String systemBusiness) {
        this.systemBusiness = systemBusiness;
    }

    @Override
    public String toString() {
        return "AccessDto{" +
                "id=" + id +
                ", systemCode='" + systemCode + '\'' +
                ", systemName='" + systemName + '\'' +
                ", loginUrl='" + loginUrl + '\'' +
                ", systemBusiness='" + systemBusiness + '\'' +
                ", systemMark='" + systemMark + '\'' +
                ", sysBusines=" + sysBusines +
                '}';
    }
}
