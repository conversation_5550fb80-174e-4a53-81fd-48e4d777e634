package com.rzl.workbenche.ops.domain;

import com.rzl.workbenche.common.annotation.Excel;
import com.rzl.workbenche.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;

/**
 * 电量分析报表对象 rpt_ele_cost_average
 *
 * <AUTHOR>
 * @date 2023-09-08
 */
public class RptEleCostAverage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    private Long id;
    /** 所属月份 */
    @Excel(name = "所属月份")
    private String rptMonth;

    /** 所属省份 */
    @Excel(name = "所属省份")
    @NotBlank(message = "省名称不能为空")
    private String prvName;

    /** 所属地市 */
    @Excel(name = "所属地市")
    @NotBlank(message = "地市名称不能为空")
    private String pregName;


    /** 平均费用类型 */
//    @Excel(name = "平均费用类型",dictType = "ele_type")
//    @NotBlank(message = "平均费用类型不能为空")
    private String eleType;

    /** 业务类型 */
    @Excel(name = "业务类型",dictType = "buss_type")
    @NotBlank(message = "业务类型不能为空")
    private String bussType;

    /** 站点业务类型 */
    @Excel(name = "站点业务类型",dictType = "rpt_type")
    @NotBlank(message = "站点业务类型不能为空")
    private String rptType;

    /** 非包干电费 */
    @Excel(name = "非包干电费(万元)")
    @NotBlank(message = "非包干电费不能为空")
    private String eleFee;

    /** 非包干电量 */
    @Excel(name = "非包干电量(万度)")
    @NotBlank(message = "非包干电量不能为空")
    private String eleAmount;

    /** 平均用电成本 */
    @Excel(name = "平均用电成本(元/度)")
    @NotBlank(message = "平均用电成本不能为空")
    private String eleCostAvg;

    /** 去地域电价系数 */
    @Excel(name = "去地域电价系数")
    @NotBlank(message = "去地域电价系数不能为空")
    private String eleRegCostRate;

    /** 直供电电费 */
    @Excel(name = "直供电电费(万元)")
    @NotBlank(message = "直供电电费不能为空")
    private String eleStraightFee;

    /** 直供电电量 */
    @Excel(name = "直供电电量(万度)")
    @NotBlank(message = "直供电电量不能为空")
    private String eleStraightAmount;

    /** 直供电平均用电成本 */
    @Excel(name = "直供电平均用电成本(元/度)")
    @NotBlank(message = "直供电平均用电成本不能为空")
    private String eleStraightCostAverage;

    /** 直供电去地域电价系数 */
    @Excel(name = "直供电去地域电价系数")
    @NotBlank(message = "直供电去地域电价系数不能为空")
    private String eleStraightRegCostRate;

    /** 转供电电费 */
    @Excel(name = "转供电电费(万元)")
    @NotBlank(message = "转供电电费不能为空")
    private String eleTransferFee;

    /** 转供电电量 */
    @Excel(name = "转供电电量(万度)")
    @NotBlank(message = "转供电电量不能为空")
    private String eleTransferAmount;

    /** 转供电平均用电成本 */
    @Excel(name = "转供电平均用电成本(元/度)")
    @NotBlank(message = "转供电平均用电成本不能为空")
    private String eleTransferCostAverage;

    /** 转供电去地域电价系数 */
    @Excel(name = "转供电去地域电价系数")
    @NotBlank(message = "转供电去地域电价系数不能为空")
    private String eleTransferRegCostRate;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setPrvName(String prvName)
    {
        this.prvName = prvName;
    }

    public String getPrvName()
    {
        return prvName;
    }
    public void setPregName(String pregName)
    {
        this.pregName = pregName;
    }

    public String getPregName()
    {
        return pregName;
    }

    public String getEleType() {
        return eleType;
    }

    public void setEleType(String eleType) {
        this.eleType = eleType;
    }

    public void setBussType(String bussType)
    {
        this.bussType = bussType;
    }

    public String getBussType()
    {
        return bussType;
    }
    public void setRptType(String rptType)
    {
        this.rptType = rptType;
    }

    public String getRptType()
    {
        return rptType;
    }
    public void setEleFee(String eleFee)
    {
        this.eleFee = eleFee;
    }

    public String getEleFee()
    {
        return eleFee;
    }
    public void setEleAmount(String eleAmount)
    {
        this.eleAmount = eleAmount;
    }

    public String getEleAmount()
    {
        return eleAmount;
    }
    public void setEleCostAvg(String eleCostAvg)
    {
        this.eleCostAvg = eleCostAvg;
    }

    public String getEleCostAvg()
    {
        return eleCostAvg;
    }
    public void setEleRegCostRate(String eleRegCostRate)
    {
        this.eleRegCostRate = eleRegCostRate;
    }

    public String getEleRegCostRate()
    {
        return eleRegCostRate;
    }
    public void setEleStraightFee(String eleStraightFee)
    {
        this.eleStraightFee = eleStraightFee;
    }

    public String getEleStraightFee()
    {
        return eleStraightFee;
    }
    public void setEleStraightAmount(String eleStraightAmount)
    {
        this.eleStraightAmount = eleStraightAmount;
    }

    public String getEleStraightAmount()
    {
        return eleStraightAmount;
    }
    public void setEleStraightCostAverage(String eleStraightCostAverage)
    {
        this.eleStraightCostAverage = eleStraightCostAverage;
    }

    public String getEleStraightCostAverage()
    {
        return eleStraightCostAverage;
    }
    public void setEleStraightRegCostRate(String eleStraightRegCostRate)
    {
        this.eleStraightRegCostRate = eleStraightRegCostRate;
    }

    public String getEleStraightRegCostRate()
    {
        return eleStraightRegCostRate;
    }
    public void setEleTransferFee(String eleTransferFee)
    {
        this.eleTransferFee = eleTransferFee;
    }

    public String getEleTransferFee()
    {
        return eleTransferFee;
    }
    public void setEleTransferAmount(String eleTransferAmount)
    {
        this.eleTransferAmount = eleTransferAmount;
    }

    public String getEleTransferAmount()
    {
        return eleTransferAmount;
    }
    public void setEleTransferCostAverage(String eleTransferCostAverage)
    {
        this.eleTransferCostAverage = eleTransferCostAverage;
    }

    public String getEleTransferCostAverage()
    {
        return eleTransferCostAverage;
    }
    public void setEleTransferRegCostRate(String eleTransferRegCostRate)
    {
        this.eleTransferRegCostRate = eleTransferRegCostRate;
    }

    public String getEleTransferRegCostRate()
    {
        return eleTransferRegCostRate;
    }
    public void setRptMonth(String rptMonth)
    {
        this.rptMonth = rptMonth;
    }

    public String getRptMonth()
    {
        return rptMonth;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("prvName", getPrvName())
            .append("pregName", getPregName())
            .append("bussType", getBussType())
            .append("rptType", getRptType())
            .append("eleFee", getEleFee())
            .append("eleAmount", getEleAmount())
            .append("eleCostAvg", getEleCostAvg())
            .append("eleRegCostRate", getEleRegCostRate())
            .append("eleStraightFee", getEleStraightFee())
            .append("eleStraightAmount", getEleStraightAmount())
            .append("eleStraightCostAverage", getEleStraightCostAverage())
            .append("eleStraightRegCostRate", getEleStraightRegCostRate())
            .append("eleTransferFee", getEleTransferFee())
            .append("eleTransferAmount", getEleTransferAmount())
            .append("eleTransferCostAverage", getEleTransferCostAverage())
            .append("eleTransferRegCostRate", getEleTransferRegCostRate())
            .append("rptMonth", getRptMonth())
            .toString();
    }
}
