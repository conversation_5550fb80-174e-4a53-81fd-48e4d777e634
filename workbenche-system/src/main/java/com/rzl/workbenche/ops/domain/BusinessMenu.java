package com.rzl.workbenche.ops.domain;

import com.rzl.workbenche.common.annotation.Excel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 业务菜单对象 business_menu
 *
 * <AUTHOR>
 * @date 2023-11-09
 */
public class BusinessMenu
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 菜单名称 */
    @Excel(name = "菜单名称")
    private String name;

    /** 组件路径 */
    @Excel(name = "组件路径")
    private String path;

    /** 路由 */
    @Excel(name = "路由")
    private String component;

    /** 路由参数 */
    @Excel(name = "路由参数")
    private String query;

    private Meta meta;

    private String systemCode;

    private String isChoose;

    public String getIsChoose() {
        return isChoose;
    }

    public void setIsChoose(String isChoose) {
        this.isChoose = isChoose;
    }

    public Meta getMeta() {
        return meta;
    }

    public void setMeta(Meta meta) {
        this.meta = meta;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }
    public void setPath(String path)
    {
        this.path = path;
    }

    public String getPath()
    {
        return path;
    }
    public void setComponent(String component)
    {
        this.component = component;
    }

    public String getComponent()
    {
        return component;
    }
    public void setQuery(String query)
    {
        this.query = query;
    }

    public String getQuery()
    {
        return query;
    }


    public void setSystemCode(String systemCode)
    {
        this.systemCode = systemCode;
    }

    public String getSystemCode()
    {
        return systemCode;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("path", getPath())
            .append("component", getComponent())
            .append("query", getQuery())
            .append("systemCode", getSystemCode())
            .toString();
    }
}
