package com.rzl.workbenche.ops.mapper;

import java.util.List;
import com.rzl.workbenche.ops.domain.OpsSystemGuide;

/**
 * 无感接入系统向导配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-11-09
 */
public interface OpsSystemGuideMapper 
{
    /**
     * 查询无感接入系统向导配置
     * 
     * @param id 无感接入系统向导配置主键
     * @return 无感接入系统向导配置
     */
    public OpsSystemGuide selectOpsSystemGuideById(Long id);

    /**
     * 查询无感接入系统向导配置列表
     * 
     * @param opsSystemGuide 无感接入系统向导配置
     * @return 无感接入系统向导配置集合
     */
    public List<OpsSystemGuide> selectOpsSystemGuideList(OpsSystemGuide opsSystemGuide);

    /**
     * 新增无感接入系统向导配置
     * 
     * @param opsSystemGuide 无感接入系统向导配置
     * @return 结果
     */
    public int insertOpsSystemGuide(OpsSystemGuide opsSystemGuide);

    /**
     * 修改无感接入系统向导配置
     * 
     * @param opsSystemGuide 无感接入系统向导配置
     * @return 结果
     */
    public int updateOpsSystemGuide(OpsSystemGuide opsSystemGuide);

    /**
     * 删除无感接入系统向导配置
     * 
     * @param id 无感接入系统向导配置主键
     * @return 结果
     */
    public int deleteOpsSystemGuideById(Long id);

    /**
     * 批量删除无感接入系统向导配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOpsSystemGuideByIds(Long[] ids);


    OpsSystemGuide selectBySystemCode(String systemCode);

    OpsSystemGuide selectBySystemMark(String systemMark);

}