package com.rzl.workbenche.ops.service.impl;

import java.util.List;

import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.utils.ListUtil;
import com.rzl.workbenche.common.utils.ParamParse;
import com.rzl.workbenche.common.utils.StrUtils;
import com.rzl.workbenche.common.utils.ValidationUtils;
import com.rzl.workbenche.ops.domain.vo.DataVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rzl.workbenche.ops.mapper.RptEleCostAverageMapper;
import com.rzl.workbenche.ops.domain.RptEleCostAverage;
import com.rzl.workbenche.ops.service.IRptEleCostAverageService;

/**
 * 电量分析报表Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-08
 */
@Service
public class RptEleCostAverageServiceImpl implements IRptEleCostAverageService
{
    @Autowired
    private RptEleCostAverageMapper rptEleCostAverageMapper;

    /**
     * 查询电量分析报表
     *
     * @param id 电量分析报表主键
     * @return 电量分析报表
     */
    @Override
    public RptEleCostAverage selectRptEleCostAverageByPrvName(Long id)
    {
        return rptEleCostAverageMapper.selectRptEleCostAverageByPrvName(id);
    }

    /**
     * 查询电量分析报表列表
     *
     * @param rptEleCostAverage 电量分析报表
     * @return 电量分析报表
     */
    @Override
    public List<RptEleCostAverage> selectRptEleCostAverageList(RptEleCostAverage rptEleCostAverage)
    {
        return rptEleCostAverageMapper.selectRptEleCostAverageList(rptEleCostAverage);
    }

    /**
     * 新增电量分析报表
     *
     * @param rptEleCostAverage 电量分析报表
     * @return 结果
     */
    @Override
    public int insertRptEleCostAverage(RptEleCostAverage rptEleCostAverage)
    {
        return rptEleCostAverageMapper.insertRptEleCostAverage(rptEleCostAverage);
    }

    /**
     * 修改电量分析报表
     *
     * @param rptEleCostAverage 电量分析报表
     * @return 结果
     */
    @Override
    public int updateRptEleCostAverage(RptEleCostAverage rptEleCostAverage)
    {
        return rptEleCostAverageMapper.updateRptEleCostAverage(rptEleCostAverage);
    }

    /**
     * 批量删除电量分析报表
     *
     * @param ids 需要删除的电量分析报表主键
     * @return 结果
     */
    @Override
    public int deleteRptEleCostAverageByPrvNames(Long[] ids)
    {
        return rptEleCostAverageMapper.deleteRptEleCostAverageByPrvNames(ids);
    }

    /**
     * 删除电量分析报表信息
     *
     * @param id 电量分析报表主键
     * @return 结果
     */
    @Override
    public int deleteRptEleCostAverageByPrvName(Long id)
    {
        return rptEleCostAverageMapper.deleteRptEleCostAverageByPrvName(id);
    }

    @Override
    public AjaxResult saveBatchRptEleCostAverage(DataVo dataVo) {
        List<RptEleCostAverage> averages = ParamParse.parseClass(dataVo.getData(), RptEleCostAverage.class);
        for (RptEleCostAverage average : averages) {
            String msg = ValidationUtils.validateObject(average);
            if (StrUtils.isNotNull(msg)){
                return AjaxResult.error(msg);
            }
        }
        List<List<RptEleCostAverage>> convert = ListUtil.convert(averages);
        convert.forEach(costAverages->rptEleCostAverageMapper.saveBatchRptEleCostAverage(costAverages));
        return AjaxResult.success();
    }
}
