package com.rzl.workbenche.ops.domain.vo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

public class UserVo {

    @NotBlank(message = "用户名不能为空")
    private String username;

    @NotBlank(message = "电话不能为空")
    @Pattern(regexp = "^([1][3-9][0-9]{9})|(0\\d{2,3}-?\\d{7,8})$",message = "手机号格式不符合规范")
    private String phoneNumber;

    @NotBlank(message = "验证码不能为空")
    private String code;

    @NotBlank(message = "新密码不能为空")
    private String newPassword;

    @NotBlank(message = "确认密码不能为空")
    private String againPassword;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getNewPassword() {
        return newPassword;
    }

    public void setNewPassword(String newPassword) {
        this.newPassword = newPassword;
    }

    public String getAgainPassword() {
        return againPassword;
    }

    public void setAgainPassword(String againPassword) {
        this.againPassword = againPassword;
    }
}
