package com.rzl.workbenche.ops.domain;

import com.rzl.workbenche.common.annotation.Excel;
import com.rzl.workbenche.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;

/**
 * 采集和同步接口管理对象 ops_interface
 *
 * <AUTHOR>
 * @date 2023-08-24
 */
public class OpsCollectInterface extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 所属系统 */
    private String system;
    @Excel(name = "业务系统")
    private String systemName;

    /** 接口名称 */
    @Excel(name = "接口名称")
    @NotBlank(message = "接口名称不能为空")
    private String interfaceName;

    /** 接口地址 */
    @Excel(name = "接口地址(url)")
    @NotBlank(message = "接口地址不能为空")
    private String interfaceUrl;

    /** 接口类型 */
    @Excel(name = "接口类型",readConverterExp = "1=get,2=post")
    private String interfaceType;

    /** 接口状态 */
    @Excel(name = "接口状态",readConverterExp = "1=启用,0=停用")
    private String status;

    /** 接口分类 1：采集 2：同步 */
//    @Excel(name = "接口分类 1：采集 2：同步")
    private String type;

    /** 是否删除 0：正常 1：删除 */
    private Integer delFlag;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setSystem(String system)
    {
        this.system = system;
    }

    public String getSystem()
    {
        return system;
    }
    public void setInterfaceName(String interfaceName)
    {
        this.interfaceName = interfaceName;
    }

    public String getInterfaceName()
    {
        return interfaceName;
    }
    public void setInterfaceUrl(String interfaceUrl)
    {
        this.interfaceUrl = interfaceUrl;
    }

    public String getInterfaceUrl()
    {
        return interfaceUrl;
    }
    public void setInterfaceType(String interfaceType)
    {
        this.interfaceType = interfaceType;
    }

    public String getInterfaceType()
    {
        return interfaceType;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setType(String type)
    {
        this.type = type;
    }

    public String getType()
    {
        return type;
    }
    public void setDelFlag(Integer delFlag)
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag()
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("system", getSystem())
            .append("interfaceName", getInterfaceName())
            .append("interfaceUrl", getInterfaceUrl())
            .append("interfaceType", getInterfaceType())
            .append("status", getStatus())
            .append("type", getType())
            .append("delFlag", getDelFlag())
            .toString();
    }
}
