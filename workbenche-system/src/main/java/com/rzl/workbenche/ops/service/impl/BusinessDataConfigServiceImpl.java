package com.rzl.workbenche.ops.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import com.rzl.workbenche.common.constant.Constants;
import com.rzl.workbenche.common.core.domain.entity.SysMenu;
import com.rzl.workbenche.common.exception.ServiceException;
import com.rzl.workbenche.common.utils.DateTimeUtils;
import com.rzl.workbenche.common.utils.StrUtils;
import com.rzl.workbenche.ops.domain.OpsSystemAccess;
import com.rzl.workbenche.ops.domain.OpsSystemGuide;
import com.rzl.workbenche.ops.domain.vo.SystemConfigVo;
import com.rzl.workbenche.ops.mapper.OpsSystemAccessMapper;
import com.rzl.workbenche.ops.mapper.OpsSystemGuideMapper;
import com.rzl.workbenche.system.mapper.SysMenuMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.rzl.workbenche.ops.mapper.BusinessDataConfigMapper;
import com.rzl.workbenche.ops.domain.BusinessDataConfig;
import com.rzl.workbenche.ops.service.IBusinessDataConfigService;

/**
 * 系统数据接口传递Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-22
 */
@Service
public class BusinessDataConfigServiceImpl implements IBusinessDataConfigService
{
    @Autowired
    private BusinessDataConfigMapper businessDataConfigMapper;

    @Autowired
    private SysMenuMapper menuMapper;

    @Value("${sign.publicKey}")
    private String opsPub;

    @Autowired
    private OpsSystemAccessMapper accessMapper;

    @Autowired
    private OpsSystemGuideMapper guideMapper;
    /**
     * 查询系统数据接口传递
     *
     * @param id 系统数据接口传递主键
     * @return 系统数据接口传递
     */
    @Override
    public BusinessDataConfig selectBusinessDataConfigById(Long id)
    {
        return businessDataConfigMapper.selectBusinessDataConfigById(id);
    }

    /**
     * 查询系统数据接口传递列表
     *
     * @param businessDataConfig 系统数据接口传递
     * @return 系统数据接口传递
     */
    @Override
    public List<BusinessDataConfig> selectBusinessDataConfigList(BusinessDataConfig businessDataConfig)
    {

        return businessDataConfigMapper.selectBusinessDataConfigList(businessDataConfig);
    }

    /**
     * 新增系统数据接口传递
     *
     * @param businessDataConfig 系统数据接口传递
     * @return 结果
     */
    @Override
    public int insertBusinessDataConfig(BusinessDataConfig businessDataConfig)
    {
        BusinessDataConfig dataConfig = businessDataConfigMapper.selectBySystem(businessDataConfig.getSystemCode());
        if (!StrUtils.isNull(businessDataConfig.getSystemUrl()) && businessDataConfig.getSystemUrl().length()>255){
            throw new ServiceException("输入字符过长");
        }
        if (!StrUtils.isNull(businessDataConfig.getSystemUrl()) && !businessDataConfig.getSystemUrl().startsWith("http")){
            throw new ServiceException("接口地址必须以http开头");
        }
        if (businessDataConfig.getSystemName().length()>255 || businessDataConfig.getSystemCode().length()>255) {
            throw new ServiceException("输入字符过长");
        }
        if (Objects.nonNull(dataConfig)){
            throw new ServiceException("该系统已配置");
        }
        businessDataConfig.setCreateTime(DateTimeUtils.getNowDate());
        businessDataConfig.setOpsPub(opsPub);
        return businessDataConfigMapper.insertBusinessDataConfig(businessDataConfig);
    }

    /**
     * 修改系统数据接口传递
     *
     * @param businessDataConfig 系统数据接口传递
     * @return 结果
     */
    @Override
    public int updateBusinessDataConfig(BusinessDataConfig businessDataConfig)
    {
        if (!StrUtils.isNull(businessDataConfig.getSystemUrl()) && businessDataConfig.getSystemUrl().length()>255){
            throw new ServiceException("输入字符过长");
        }
        if (!StrUtils.isNull(businessDataConfig.getSystemUrl()) && !businessDataConfig.getSystemUrl().startsWith("http")){
            throw new ServiceException("接口地址必须以http开头");
        }
        if (businessDataConfig.getSystemName().length()>255 || businessDataConfig.getSystemCode().length()>255) {
            throw new ServiceException("输入字符过长");
        }
        BusinessDataConfig dataConfig = businessDataConfigMapper.selectBusinessDataConfigById(businessDataConfig.getId());
        if (!dataConfig.getSystemName().equals(businessDataConfig.getSystemName())){
            //修改菜单
            SysMenu menu = menuMapper.selectByMenuName(dataConfig.getSystemName(), Constants.WUJIE_COMPONENT,dataConfig.getSystemCode());
            SysMenu menu1 = menuMapper.selectByMenuName(dataConfig.getSystemName(), Constants.IFRAME_COMPONENT,dataConfig.getSystemCode());
            if (Objects.nonNull(menu)){
                menu.setMenuName(businessDataConfig.getSystemName());
                menuMapper.updateMenu(menu);
            }
            if (Objects.nonNull(menu1)){
                menu1.setMenuName(businessDataConfig.getSystemName());
                menuMapper.updateMenu(menu1);
            }
        }
        return businessDataConfigMapper.updateBusinessDataConfig(businessDataConfig);
    }

    /**
     * 批量删除系统数据接口传递
     *
     * @param ids 需要删除的系统数据接口传递主键
     * @return 结果
     */
    @Override
    public int deleteBusinessDataConfigByIds(Long[] ids)
    {
        return businessDataConfigMapper.deleteBusinessDataConfigByIds(ids);
    }

    /**
     * 删除系统数据接口传递信息
     *
     * @param id 系统数据接口传递主键
     * @return 结果
     */
    @Override
    public int deleteBusinessDataConfigById(Long id)
    {
        BusinessDataConfig dataConfig = businessDataConfigMapper.selectBusinessDataConfigById(id);
        OpsSystemAccess systemAccess = accessMapper.selctBySystem(dataConfig.getSystemCode());
        OpsSystemGuide systemGuide = guideMapper.selectBySystemCode(dataConfig.getSystemCode());
        if (StrUtils.isNotNull(systemAccess) || StrUtils.isNotNull(systemGuide)){
            throw new ServiceException("该系统已接入，不允许删除");
        }
        return businessDataConfigMapper.deleteBusinessDataConfigById(id);
    }

    /**
     * 获取下拉菜单
     * @param businessDataConfig
     * @return
     */
    @Override
    public List<SystemConfigVo> selectConfigList(BusinessDataConfig businessDataConfig) {
        List<BusinessDataConfig> dataConfigs = businessDataConfigMapper.selectBusinessDataConfigList(new BusinessDataConfig());
        List<SystemConfigVo> configVos = new ArrayList<>();
        dataConfigs.forEach(dataConfig->{
            SystemConfigVo systemConfigVo = new SystemConfigVo();
            BeanUtils.copyProperties(dataConfig,systemConfigVo);
            configVos.add(systemConfigVo);
        });
        return configVos;
    }
}
