package com.rzl.workbenche.ops.service;

import com.rzl.workbenche.ops.domain.SystemAnalysis;

import java.util.List;


/**
 * 系统情况分析Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
public interface ISystemAnalysisService 
{
    /**
     * 查询系统情况分析
     * 
     * @param id 系统情况分析主键
     * @return 系统情况分析
     */
    public SystemAnalysis selectSystemAnalysisById(Long id);

    /**
     * 查询系统情况分析列表
     * 
     * @param systemAnalysis 系统情况分析
     * @return 系统情况分析集合
     */
    public List<SystemAnalysis> selectSystemAnalysisList(SystemAnalysis systemAnalysis);

    /**
     * 新增系统情况分析
     * 
     * @param systemAnalysis 系统情况分析
     * @return 结果
     */
    public int insertSystemAnalysis(SystemAnalysis systemAnalysis);

    /**
     * 修改系统情况分析
     * 
     * @param systemAnalysis 系统情况分析
     * @return 结果
     */
    public int updateSystemAnalysis(SystemAnalysis systemAnalysis);

    /**
     * 批量删除系统情况分析
     * 
     * @param ids 需要删除的系统情况分析主键集合
     * @return 结果
     */
    public int deleteSystemAnalysisByIds(Long[] ids);

    /**
     * 删除系统情况分析信息
     * 
     * @param id 系统情况分析主键
     * @return 结果
     */
    public int deleteSystemAnalysisById(Long id);
}
