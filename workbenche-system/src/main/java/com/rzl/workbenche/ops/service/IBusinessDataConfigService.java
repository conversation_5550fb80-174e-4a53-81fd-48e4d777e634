package com.rzl.workbenche.ops.service;

import java.util.List;
import com.rzl.workbenche.ops.domain.BusinessDataConfig;
import com.rzl.workbenche.ops.domain.vo.SystemConfigVo;

/**
 * 系统数据接口传递Service接口
 * 
 * <AUTHOR>
 * @date 2023-11-22
 */
public interface IBusinessDataConfigService 
{
    /**
     * 查询系统数据接口传递
     * 
     * @param id 系统数据接口传递主键
     * @return 系统数据接口传递
     */
    public BusinessDataConfig selectBusinessDataConfigById(Long id);

    /**
     * 查询系统数据接口传递列表
     * 
     * @param businessDataConfig 系统数据接口传递
     * @return 系统数据接口传递集合
     */
    public List<BusinessDataConfig> selectBusinessDataConfigList(BusinessDataConfig businessDataConfig);

    /**
     * 新增系统数据接口传递
     * 
     * @param businessDataConfig 系统数据接口传递
     * @return 结果
     */
    public int insertBusinessDataConfig(BusinessDataConfig businessDataConfig);

    /**
     * 修改系统数据接口传递
     * 
     * @param businessDataConfig 系统数据接口传递
     * @return 结果
     */
    public int updateBusinessDataConfig(BusinessDataConfig businessDataConfig);

    /**
     * 批量删除系统数据接口传递
     * 
     * @param ids 需要删除的系统数据接口传递主键集合
     * @return 结果
     */
    public int deleteBusinessDataConfigByIds(Long[] ids);

    /**
     * 删除系统数据接口传递信息
     * 
     * @param id 系统数据接口传递主键
     * @return 结果
     */
    public int deleteBusinessDataConfigById(Long id);

    List<SystemConfigVo> selectConfigList(BusinessDataConfig businessDataConfig);
}