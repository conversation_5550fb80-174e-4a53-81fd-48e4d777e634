package com.rzl.workbenche.ops.domain;

import com.rzl.workbenche.common.annotation.Excel;
import com.rzl.workbenche.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 告警与待办效率分析对象 analysis
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
public class AlarmAnalysis extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 所属系统 */

    private String system;
    @Excel(name = "业务系统")
    private String systemName;

    /** 月份 */
    @Excel(name = "月份")
    private String month;

    /** 待办总数 */
    @Excel(name = "告警总数")
    private Long total;

    /** 已办数量 */
    @Excel(name = "已办数量")
    private Long doneTotal;

    /** 耗时 */
    private Long duration;

    @Excel(name = "耗时")
    private String dura;

    /** 处理人 */
    private String user;

    /** 待办：1  告警：2 */
    private String type;

    public String getDura() {
        return dura;
    }

    public void setDura(String dura) {
        this.dura = dura;
    }

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setSystem(String system) 
    {
        this.system = system;
    }

    public String getSystem() 
    {
        return system;
    }
    public void setMonth(String month) 
    {
        this.month = month;
    }

    public String getMonth() 
    {
        return month;
    }
    public void setTotal(Long total) 
    {
        this.total = total;
    }

    public Long getTotal() 
    {
        return total;
    }
    public void setDoneTotal(Long doneTotal) 
    {
        this.doneTotal = doneTotal;
    }

    public Long getDoneTotal() 
    {
        return doneTotal;
    }
    public void setDuration(Long duration) 
    {
        this.duration = duration;
    }

    public Long getDuration() 
    {
        return duration;
    }
    public void setUser(String user) 
    {
        this.user = user;
    }

    public String getUser() 
    {
        return user;
    }
    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("system", getSystem())
            .append("month", getMonth())
            .append("total", getTotal())
            .append("doneTotal", getDoneTotal())
            .append("duration", getDuration())
            .append("user", getUser())
            .append("type", getType())
            .toString();
    }
}
