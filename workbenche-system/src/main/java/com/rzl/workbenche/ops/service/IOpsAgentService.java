package com.rzl.workbenche.ops.service;

import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.ops.domain.OpsAgent;
import com.rzl.workbenche.ops.domain.export.OpsAgentExport;
import com.rzl.workbenche.ops.domain.vo.DataVo;


import java.util.List;


/**
 * 运维待办管理Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-23
 */
public interface IOpsAgentService
{
    /**
     * 查询运维待办管理
     * 
     * @param id 运维待办管理主键
     * @return 运维待办管理
     */
    public OpsAgent selectOpsDoById(Long id);

    /**
     * 查询运维待办管理列表
     *
     * @param opsAgent 运维待办管理
     * @return 运维待办管理集合
     */
    public List<OpsAgent> selectOpsDoList(OpsAgent opsAgent);

    /**
     * 新增运维待办管理
     * 
     * @param opsAgent 运维待办管理
     * @return 结果
     */
    public int insertOpsDo(OpsAgent opsAgent);

    /**
     * 修改运维待办管理
     * 
     * @param opsAgent 运维待办管理
     * @return 结果
     */
    public int updateOpsDo(OpsAgent opsAgent);

    /**
     * 批量删除运维待办管理
     * 
     * @param ids 需要删除的运维待办管理主键集合
     * @return 结果
     */
    public int deleteOpsDoByIds(Long[] ids);

    /**
     * 删除运维待办管理信息
     * 
     * @param id 运维待办管理主键
     * @return 结果
     */
    public int deleteOpsDoById(Long id);
    /**
     * 运维待办管理处理
     * @param opsAgent
     * @return
     */
    AjaxResult handle(OpsAgent opsAgent);

    /**
     * 导出查询
     * @param opsAgent
     * @return
     */
    List<OpsAgentExport> selectOpsDoListExport(OpsAgent opsAgent);

    /**
     * 批量处理
     * @param ids
     * @param opinion
     * @return
     */
    AjaxResult batchHandle(List<Long> ids, String opinion);
    Long selectCount(OpsAgent opsAgent);

    AjaxResult insertAgent(OpsAgent agent);

    AjaxResult saveBatchAgent(DataVo opsAgents);

    AjaxResult updateAgentStatus(DataVo dataVo);
}
