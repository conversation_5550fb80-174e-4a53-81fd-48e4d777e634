package com.rzl.workbenche.ops.domain.vo;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

public class DataVo implements Serializable {

    @NotBlank(message = "签名不能为空")
    private String sign;

    @NotBlank(message = "domain不能为空")
    private String domain;

    @NotBlank(message = "系统不能为空")
    private String system;

    private String data;

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getSystem() {
        return system;
    }

    public void setSystem(String system) {
        this.system = system;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }
}
