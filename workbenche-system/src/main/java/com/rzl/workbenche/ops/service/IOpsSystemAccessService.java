package com.rzl.workbenche.ops.service;

import java.util.List;
import com.rzl.workbenche.ops.domain.OpsSystemAccess;
import com.rzl.workbenche.ops.domain.dto.AccessDto;

/**
 * 系统接入Service接口
 * 
 * <AUTHOR>
 * @date 2023-10-18
 */
public interface IOpsSystemAccessService 
{
    /**
     * 查询系统接入
     * 
     * @param id 系统接入主键
     * @return 系统接入
     */
    public OpsSystemAccess selectOpsSystemAccessById(Long id);

    /**
     * 查询系统接入列表
     * 
     * @param opsSystemAccess 系统接入
     * @return 系统接入集合
     */
    public List<OpsSystemAccess> selectOpsSystemAccessList(OpsSystemAccess opsSystemAccess);

    /**
     * 新增系统接入
     * 
     * @param accessDto 系统接入
     * @return 结果
     */
    public int insertOpsSystemAccess(AccessDto accessDto);

    /**
     * 修改系统接入
     * 
     * @param accessDto 系统接入
     * @return 结果
     */
    public int updateOpsSystemAccess(AccessDto accessDto);

    /**
     * 批量删除系统接入
     * 
     * @param ids 需要删除的系统接入主键集合
     * @return 结果
     */
    public int deleteOpsSystemAccessByIds(Long[] ids);

    /**
     * 删除系统接入信息
     * 
     * @param id 系统接入主键
     * @return 结果
     */
    public int deleteOpsSystemAccessById(Long id);
}