package com.rzl.workbenche.ops.service.impl;

import com.rzl.workbenche.ops.domain.AlarmAnalysis;
import com.rzl.workbenche.ops.mapper.AlarmAnalysisMapper;
import com.rzl.workbenche.ops.service.IAlarmAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 告警效率分析Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
@Service
public class AlarmAnalysisServiceImpl implements IAlarmAnalysisService
{
    @Autowired
    private AlarmAnalysisMapper analysisMapper;

    /**
     * 查询告警效率分析
     * 
     * @param id 告警效率分析主键
     * @return 告警效率分析
     */
    @Override
    public AlarmAnalysis selectAlarmAnalysisById(Long id)
    {
        return analysisMapper.selectAlarmAnalysisById(id);
    }

    /**
     * 查询告警效率分析列表
     * 
     * @param analysis 告警效率分析
     * @return 告警效率分析
     */
    @Override
    public List<AlarmAnalysis> selectAlarmAnalysisList(AlarmAnalysis analysis)
    {
        return analysisMapper.selectAlarmAnalysisList(analysis);
    }

    /**
     * 新增告警效率分析
     * 
     * @param analysis 告警效率分析
     * @return 结果
     */
    @Override
    public int insertAlarmAnalysis(AlarmAnalysis analysis)
    {
        return analysisMapper.insertAlarmAnalysis(analysis);
    }

    /**
     * 修改告警效率分析
     * 
     * @param analysis 告警效率分析
     * @return 结果
     */
    @Override
    public int updateAlarmAnalysis(AlarmAnalysis analysis)
    {
        return analysisMapper.updateAlarmAnalysis(analysis);
    }

    /**
     * 批量删除告警效率分析
     * 
     * @param ids 需要删除的告警效率分析主键
     * @return 结果
     */
    @Override
    public int deleteAlarmAnalysisByIds(Long[] ids)
    {
        return analysisMapper.deleteAlarmAnalysisByIds(ids);
    }

    /**
     * 删除告警效率分析信息
     * 
     * @param id 告警效率分析主键
     * @return 结果
     */
    @Override
    public int deleteAlarmAnalysisById(Long id)
    {
        return analysisMapper.deleteAlarmAnalysisById(id);
    }
}
