package com.rzl.workbenche.ops.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.utils.*;
import com.rzl.workbenche.ops.domain.OpsAgent;
import com.rzl.workbenche.ops.domain.export.OpsAgentExport;
import com.rzl.workbenche.ops.domain.vo.DataVo;
import com.rzl.workbenche.ops.mapper.OpsAgentMapper;
import com.rzl.workbenche.ops.service.IOpsAgentService;
import com.rzl.workbenche.system.mapper.SysRegionMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;

/**
 * 运维待办管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-23
 */
@Service
public class OpsAgentServiceImpl implements IOpsAgentService
{
    @Autowired
    private OpsAgentMapper opsAgentMapper;

    @Autowired
    private SysRegionMapper regionMapper;
    /**
     * 查询运维待办管理
     *
     * @param id 运维待办管理主键
     * @return 运维待办管理
     */
    @Override
    public OpsAgent selectOpsDoById(Long id)
    {
        OpsAgent opsAgent = opsAgentMapper.selectOpsDoById(id);
        if ("1".equals(opsAgent.getHandleStatus())){
            opsAgent.setDura(DateTimeUtils.getDatePoor(opsAgent.getDuration()));
        }
        return opsAgent;
    }

    /**
     * 查询运维待办管理列表
     *
     * @param opsAgent 运维待办管理
     * @return 运维待办管理
     */
    @Override
    public List<OpsAgent> selectOpsDoList(OpsAgent opsAgent)
    {
        opsAgent.setRegIds(SecurityUtils.getRegIds());
        return  opsAgentMapper.selectOpsDoList(opsAgent);
    }

    /**
     * 新增运维待办管理
     *
     * @param opsAgent 运维待办管理
     * @return 结果
     */
    @Override
    public int insertOpsDo(OpsAgent opsAgent)
    {
        opsAgent.setCreateTime(DateTimeUtils.getNowDate());
        return opsAgentMapper.insertOpsDo(opsAgent);
    }

    /**
     * 修改运维待办管理
     *
     * @param opsAgent 运维待办管理
     * @return 结果
     */
    @Override
    public int updateOpsDo(OpsAgent opsAgent)
    {
        return opsAgentMapper.updateOpsDo(opsAgent);
    }

    /**
     * 批量删除运维待办管理
     *
     * @param ids 需要删除的运维待办管理主键
     * @return 结果
     */
    @Override
    public int deleteOpsDoByIds(Long[] ids)
    {
        return opsAgentMapper.deleteOpsDoByIds(ids);
    }

    /**
     * 删除运维待办管理信息
     *
     * @param id 运维待办管理主键
     * @return 结果
     */
    @Override
    public int deleteOpsDoById(Long id)
    {
        return opsAgentMapper.deleteOpsDoById(id);
    }
    /**
     * 运维待办管理处理
     * @param opsAgent
     * @return
     */
    @Override
    public AjaxResult handle(OpsAgent opsAgent) {
        OpsAgent opsAgent1 = opsAgentMapper.selectOpsDoById(opsAgent.getId());
        opsAgent1.setOpinion(opsAgent.getOpinion());
        //设置处理时间
        opsAgent1.setHandleTime(DateTimeUtils.getNowDate());
        //设置处理状态
        opsAgent1.setHandleStatus("1");
        //设置处理人
        opsAgent1.setHandleUser(SecurityUtils.getUsername());
        //设置耗时
        opsAgent1.setDuration(opsAgent1.getHandleTime().getTime() - opsAgent1.getCreateTime().getTime());
        opsAgentMapper.updateOpsDo(opsAgent1);
        return AjaxResult.success();
    }

    @Override
    public List<OpsAgentExport> selectOpsDoListExport(OpsAgent opsAgent) {
        opsAgent.setRegIds(SecurityUtils.getRegIds());
        List<OpsAgent> opsAgents = opsAgentMapper.selectOpsDoList(opsAgent);
        List<OpsAgentExport> opsAgentExports = new ArrayList<>();
        opsAgents.forEach(ops->{
            OpsAgentExport opsAgentExport = new OpsAgentExport();
            BeanUtils.copyProperties(ops, opsAgentExport);
            if ("1".equals(ops.getHandleStatus())){
                opsAgentExport.setDura(DateTimeUtils.getDatePoor(ops.getDuration()));
            }
            opsAgentExports.add(opsAgentExport);
        });
        return opsAgentExports;
    }

    @Override
    public AjaxResult batchHandle(List<Long> ids, String opinion) {
        ids.forEach(id->{
            OpsAgent opsAgent1 = opsAgentMapper.selectOpsDoById(id);
            opsAgent1.setOpinion(opinion);
            //设置处理时间
            opsAgent1.setHandleTime(DateTimeUtils.getNowDate());
            //设置处理状态
            opsAgent1.setHandleStatus("1");
            //设置处理人
            opsAgent1.setHandleUser(SecurityUtils.getUsername());
            //设置耗时
            opsAgent1.setDuration(opsAgent1.getHandleTime().getTime() - opsAgent1.getCreateTime().getTime());
            opsAgentMapper.updateOpsDo(opsAgent1);
        });
        return AjaxResult.success();
    }

    @Override
    public Long selectCount(OpsAgent opsAgent) {
        return opsAgentMapper.count(opsAgent);
    }

    /**
     * 三方推送接口新增
     * @param agent
     * @return
     */
    @Override
    public AjaxResult insertAgent(OpsAgent agent) {
        opsAgentMapper.insertOpsDo(agent);
        return AjaxResult.success();
    }

    @Override
    public AjaxResult saveBatchAgent(DataVo dataVo) {
        List<OpsAgent> opsAgents = ParamParse.parseClass(dataVo.getData(), OpsAgent.class);
        for (OpsAgent opsAgent : opsAgents) {
            String msg = ValidationUtils.validateObject(opsAgent);
            if (StrUtils.isNotNull(msg)){
                return AjaxResult.error(msg);
            }
        }
        opsAgents.forEach(agent -> {
            if ("1".equals(agent.getHandleStatus())){
                agent.setDuration(agent.getHandleTime().getTime() - agent.getCreateTime().getTime());
            }
        });
        opsAgents.forEach(ops->{
            if (StrUtils.isNotEmpty(ops.getRegName())){
                String regId = regionMapper.selectRegIdByRegName(ops.getRegName());
                ops.setRegId(regId);
            }
        });
        List<List<OpsAgent>> convert = ListUtil.convert(opsAgents);
        convert.forEach(agent->opsAgentMapper.saveBatchAgent(agent));
        return AjaxResult.success();
    }

    /**
     * 待办采集
     * @param dataVo
     * @return
     */
    @Override
    public AjaxResult updateAgentStatus(DataVo dataVo) {
        List<OpsAgent> opsAgents = ParamParse.parseClass(dataVo.getData(), OpsAgent.class);
        opsAgents.forEach(agent->{
            OpsAgent opsAgent = opsAgentMapper.selectByAgentCode(agent.getAgentCode(),dataVo.getSystem());
            if (Objects.nonNull(opsAgent)){
                agent.setDuration(agent.getHandleTime().getTime() - opsAgent.getCreateTime().getTime());
                opsAgentMapper.updateOpsDoByAgentCode(agent);
            }
        });
        return AjaxResult.success();
    }
}
