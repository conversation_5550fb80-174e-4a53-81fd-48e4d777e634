package com.rzl.workbenche.ops.mapper;

import com.rzl.workbenche.ops.domain.OpsCollectInterface;

import java.util.List;


/**
 * 采集接口管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
public interface OpsCollectInterfaceMapper 
{
    /**
     * 查询采集接口管理
     * 
     * @param id 采集接口管理主键
     * @return 采集接口管理
     */
    public OpsCollectInterface selectOpsCollectInterfaceById(Long id);

    /**
     * 查询采集接口管理列表
     * 
     * @param opsCollectInterface 采集接口管理
     * @return 采集接口管理集合
     */
    public List<OpsCollectInterface> selectOpsCollectInterfaceList(OpsCollectInterface opsCollectInterface);

    /**
     * 新增采集接口管理
     * 
     * @param opsCollectInterface 采集接口管理
     * @return 结果
     */
    public int insertOpsCollectInterface(OpsCollectInterface opsCollectInterface);

    /**
     * 修改采集接口管理
     * 
     * @param opsCollectInterface 采集接口管理
     * @return 结果
     */
    public int updateOpsCollectInterface(OpsCollectInterface opsCollectInterface);

    /**
     * 删除采集接口管理
     * 
     * @param id 采集接口管理主键
     * @return 结果
     */
    public int deleteOpsCollectInterfaceById(Long id);

    /**
     * 批量删除采集接口管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOpsCollectInterfaceByIds(Long[] ids);
    /**
     * 通过接口名称查询
     */
    OpsCollectInterface selectByName(String name);
}
