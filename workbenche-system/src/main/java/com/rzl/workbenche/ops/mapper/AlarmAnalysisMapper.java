package com.rzl.workbenche.ops.mapper;

import com.rzl.workbenche.ops.domain.AlarmAnalysis;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * 告警效率分析Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
@Repository
public interface AlarmAnalysisMapper
{
    /**
     * 查询告警效率分析
     * 
     * @param id 告警效率分析主键
     * @return 告警效率分析
     */
    public AlarmAnalysis selectAlarmAnalysisById(Long id);

    /**
     * 查询告警效率分析列表
     * 
     * @param analysis 告警效率分析
     * @return 告警效率分析集合
     */
    public List<AlarmAnalysis> selectAlarmAnalysisList(AlarmAnalysis analysis);

    /**
     * 新增告警效率分析
     * 
     * @param analysis 告警效率分析
     * @return 结果
     */
    public int insertAlarmAnalysis(AlarmAnalysis analysis);

    /**
     * 修改告警效率分析
     * 
     * @param analysis 告警效率分析
     * @return 结果
     */
    public int updateAlarmAnalysis(AlarmAnalysis analysis);

    /**
     * 删除告警效率分析
     * 
     * @param id 告警效率分析主键
     * @return 结果
     */
    public int deleteAlarmAnalysisById(Long id);

    /**
     * 批量删除告警效率分析
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAlarmAnalysisByIds(Long[] ids);

    void deleteBySystem(String system);
}
