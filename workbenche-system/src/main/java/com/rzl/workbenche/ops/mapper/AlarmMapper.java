package com.rzl.workbenche.ops.mapper;

import com.rzl.workbenche.ops.domain.Alarm;
import com.rzl.workbenche.ops.domain.AlarmAnalysis;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * 运维告警管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-23
 */
@Repository
public interface AlarmMapper 
{
    /**
     * 查询运维告警管理
     * 
     * @param id 运维告警管理主键
     * @return 运维告警管理
     */
    public Alarm selectAlarmById(Long id);

    /**
     * 查询运维告警管理列表
     * 
     * @param alarm 运维告警管理
     * @return 运维告警管理集合
     */
    public List<Alarm> selectAlarmList(Alarm alarm);

    /**
     * 新增运维告警管理
     * 
     * @param alarm 运维告警管理
     * @return 结果
     */
    public int insertAlarm(Alarm alarm);

    /**
     * 修改运维告警管理
     * 
     * @param alarm 运维告警管理
     * @return 结果
     */
    public int updateAlarm(Alarm alarm);

    /**
     * 删除运维告警管理
     * 
     * @param id 运维告警管理主键
     * @return 结果
     */
    public int deleteAlarmById(Long id);

    /**
     * 批量删除运维告警管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAlarmByIds(Long[] ids);

    /**
     * 统计条数
     */
    Long count(Alarm alarm);

    void saveBatchAlarm(List<Alarm> alarms);

    void updateByCode(Alarm alarm);

    Alarm selectByAlarmCode(@Param("alarmCode") String alarmCode, @Param("system") String system);

    List<String> selectSystem();

    List<AlarmAnalysis> selectAlarmAnalysis(String system);
}
