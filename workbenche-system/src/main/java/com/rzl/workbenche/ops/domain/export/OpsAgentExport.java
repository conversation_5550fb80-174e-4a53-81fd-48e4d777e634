package com.rzl.workbenche.ops.domain.export;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rzl.workbenche.common.annotation.Excel;

import java.util.Date;

public class OpsAgentExport {
    /**
     * 待办编码
     */
    @Excel(name = "待办编号")
    private String agentCode;
    /** 所属系统 */
    @Excel(name = "业务系统")
    private String systemName;

    /** 待办名称 */
    @Excel(name = "待办名称")
    private String agentName;

    /** 待办类型 */
    @Excel(name = "待办类型")
    private String agentType;

    /** 省名称 */
    private String prvSname;

    /** 市名称 */
    @Excel(name = "地市")
    private String pregName;

    /** 区县名称 */
    @Excel(name = "区县")
    private String regName;

    /** 待办内容(富文本或json) */
    @Excel(name = "待办内容")
    private String content;

    /** 待办内容(富文本或json) */
    @Excel(name = "创建时间",width = 30,dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 起草人 */
    @Excel(name = "起草人")
    private String createUser;

    /** 当前处理人 */
    private String curtUser;

    /** 处理状态 */
    @Excel(name = "处理状态",dictType = "handle_status")
    private String handleStatus;

    /** 紧急程度 */
    @Excel(name = "紧急程度",dictType = "urgency")
    private String urgency;

    /** 是否超期 */
    @Excel(name = "是否超期",dictType = "is_overdue")
    private String isOverdue;

    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @Excel(name = "处理时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date handleTime;

    /** 处理意见 */
//    @Excel(name = "处理意见")
    private String opinion;

    /** 处理人 */
//    @Excel(name = "处理人")
    private String handleUser;

    /** 处理耗时(格式转换) */
//    @Excel(name = "处理耗时")
    private String dura;

    public String getAgentCode() {
        return agentCode;
    }

    public void setAgentCode(String agentCode) {
        this.agentCode = agentCode;
    }

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getAgentType() {
        return agentType;
    }

    public void setAgentType(String agentType) {
        this.agentType = agentType;
    }

    public String getPrvSname() {
        return prvSname;
    }

    public void setPrvSname(String prvSname) {
        this.prvSname = prvSname;
    }

    public String getPregName() {
        return pregName;
    }

    public void setPregName(String pregName) {
        this.pregName = pregName;
    }

    public String getRegName() {
        return regName;
    }

    public void setRegName(String regName) {
        this.regName = regName;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCurtUser() {
        return curtUser;
    }

    public void setCurtUser(String curtUser) {
        this.curtUser = curtUser;
    }

    public String getHandleStatus() {
        return handleStatus;
    }

    public void setHandleStatus(String handleStatus) {
        this.handleStatus = handleStatus;
    }

    public String getUrgency() {
        return urgency;
    }

    public void setUrgency(String urgency) {
        this.urgency = urgency;
    }

    public String getIsOverdue() {
        return isOverdue;
    }

    public void setIsOverdue(String isOverdue) {
        this.isOverdue = isOverdue;
    }

    public Date getHandleTime() {
        return handleTime;
    }

    public void setHandleTime(Date handleTime) {
        this.handleTime = handleTime;
    }

    public String getOpinion() {
        return opinion;
    }

    public void setOpinion(String opinion) {
        this.opinion = opinion;
    }

    public String getHandleUser() {
        return handleUser;
    }

    public void setHandleUser(String handleUser) {
        this.handleUser = handleUser;
    }

    public String getDura() {
        return dura;
    }

    public void setDura(String dura) {
        this.dura = dura;
    }
}
