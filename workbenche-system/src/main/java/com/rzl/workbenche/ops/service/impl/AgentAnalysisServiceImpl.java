package com.rzl.workbenche.ops.service.impl;

import java.util.List;

import com.rzl.workbenche.ops.domain.AgentAnalysis;
import com.rzl.workbenche.ops.mapper.AgentAnalysisMapper;
import com.rzl.workbenche.ops.service.IAgentAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 待办效率分析Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
@Service
public class AgentAnalysisServiceImpl implements IAgentAnalysisService
{
    @Autowired
    private AgentAnalysisMapper analysisMapper;

    /**
     * 查询待办效率分析
     * 
     * @param id 待办效率分析主键
     * @return 待办效率分析
     */
    @Override
    public AgentAnalysis selectAgentAnalysisById(Long id)
    {
        return analysisMapper.selectAgentAnalysisById(id);
    }

    /**
     * 查询待办效率分析列表
     * 
     * @param agentAnalysis 待办效率分析
     * @return 待办效率分析
     */
    @Override
    public List<AgentAnalysis> selectAgentAnalysisList(AgentAnalysis agentAnalysis)
    {
        return analysisMapper.selectAgentAnalysisList(agentAnalysis);
    }

    /**
     * 新增待办效率分析
     * 
     * @param agentAnalysis 待办效率分析
     * @return 结果
     */
    @Override
    public int insertAgentAnalysis(AgentAnalysis agentAnalysis)
    {
        return analysisMapper.insertAgentAnalysis(agentAnalysis);
    }

    /**
     * 修改待办效率分析
     * 
     * @param agentAnalysis 待办效率分析
     * @return 结果
     */
    @Override
    public int updateAgentAnalysis(AgentAnalysis agentAnalysis)
    {
        return analysisMapper.updateAgentAnalysis(agentAnalysis);
    }

    /**
     * 批量删除待办效率分析
     * 
     * @param ids 需要删除的待办效率分析主键
     * @return 结果
     */
    @Override
    public int deleteAgentAnalysisByIds(Long[] ids)
    {
        return analysisMapper.deleteAgentAnalysisByIds(ids);
    }

    /**
     * 删除待办效率分析信息
     * 
     * @param id 待办效率分析主键
     * @return 结果
     */
    @Override
    public int deleteAgentAnalysisById(Long id)
    {
        return analysisMapper.deleteAgentAnalysisById(id);
    }
}
