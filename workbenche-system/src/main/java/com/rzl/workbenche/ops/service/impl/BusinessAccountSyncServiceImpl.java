package com.rzl.workbenche.ops.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.exception.ServiceException;
import com.rzl.workbenche.common.utils.*;
import com.rzl.workbenche.ops.domain.vo.AccountSyncVo;
import com.rzl.workbenche.ops.domain.vo.DataVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rzl.workbenche.ops.mapper.BusinessAccountSyncMapper;
import com.rzl.workbenche.ops.domain.BusinessAccountSync;
import com.rzl.workbenche.ops.service.IBusinessAccountSyncService;
import org.springframework.util.CollectionUtils;

/**
 * 业务系统账户同步Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-21
 */
@Service
public class BusinessAccountSyncServiceImpl implements IBusinessAccountSyncService
{
    @Autowired
    private BusinessAccountSyncMapper businessAccountSyncMapper;

    /**
     * 查询业务系统账户同步
     *
     * @param id 业务系统账户同步主键
     * @return 业务系统账户同步
     */
    @Override
    public BusinessAccountSync selectBusinessAccountSyncById(Long id)
    {
        return businessAccountSyncMapper.selectBusinessAccountSyncById(id);
    }

    /**
     * 查询业务系统账户同步列表
     *
     * @param businessAccountSync 业务系统账户同步
     * @return 业务系统账户同步
     */
    @Override
    public List<BusinessAccountSync> selectBusinessAccountSyncList(BusinessAccountSync businessAccountSync)
    {
        return businessAccountSyncMapper.selectBusinessAccountSyncList(businessAccountSync);
    }

    /**
     * 新增业务系统账户同步
     *
     * @param businessAccountSync 业务系统账户同步
     * @return 结果
     */
    @Override
    public int insertBusinessAccountSync(BusinessAccountSync businessAccountSync)
    {
        return businessAccountSyncMapper.insertBusinessAccountSync(businessAccountSync);
    }

    /**
     * 修改业务系统账户同步
     *
     * @param businessAccountSync 业务系统账户同步
     * @return 结果
     */
    @Override
    public int updateBusinessAccountSync(BusinessAccountSync businessAccountSync)
    {
        businessAccountSync.setUpdateTime(DateTimeUtils.getNowDate());
        businessAccountSync.setUpdateUser(SecurityUtils.getUsername());
        return businessAccountSyncMapper.updateBusinessAccountSync(businessAccountSync);
    }

    /**
     * 批量删除业务系统账户同步
     *
     * @param ids 需要删除的业务系统账户同步主键
     * @return 结果
     */
    @Override
    public int deleteBusinessAccountSyncByIds(Long[] ids)
    {
        return businessAccountSyncMapper.deleteBusinessAccountSyncByIds(ids);
    }

    /**
     * 删除业务系统账户同步信息
     *
     * @param id 业务系统账户同步主键
     * @return 结果
     */
    @Override
    public int deleteBusinessAccountSyncById(Long id)
    {
        return businessAccountSyncMapper.deleteBusinessAccountSyncById(id);
    }

    /**
     * 账户同步
     * @param dataVo
     * @return
     */
    @Override
    public AjaxResult accountSync(DataVo dataVo) {
        try {
            Date nowDate = DateTimeUtils.getNowDate();
            List<AccountSyncVo> accountSyncVos = ParamParse.parseClass(dataVo.getData(), AccountSyncVo.class);
            for (AccountSyncVo accountSyncVo : accountSyncVos) {
                String msg = ValidationUtils.validateObject(accountSyncVo);
                if (StrUtils.isNotNull(msg)){
                    return AjaxResult.error(msg);
                }
            }
            //筛选新增用户
            List<AccountSyncVo> addAccounts = accountSyncVos
                    .stream()
                    .filter(accountSyncVo -> "1".equals(accountSyncVo.getType()))
                    .collect(Collectors.toList());
            //筛选删除用户
            List<AccountSyncVo> rmAccounts = accountSyncVos
                    .stream()
                    .filter(accountSyncVo -> "0".equals(accountSyncVo.getType()))
                    .collect(Collectors.toList());
            //筛选删除用户名称
            List<String> usernames = rmAccounts
                    .stream()
                    .map(AccountSyncVo::getUsername)
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(usernames)){
                businessAccountSyncMapper.deleteByUsernames(usernames,dataVo.getSystem());
            }
            //筛选出不存在的用户
            List<AccountSyncVo> collect = addAccounts
                    .stream()
                    .filter(accountSyncVo -> {
                BusinessAccountSync businessAccountSync = businessAccountSyncMapper.selectByUsernameAndSystem(accountSyncVo);
                return StrUtils.isNull(businessAccountSync);
            }).collect(Collectors.toList());
            //给默认状态
            if(collect.size()>0){
                collect.forEach(accountSyncVo -> {
                    accountSyncVo.setSyncTime(nowDate);
                    accountSyncVo.setStatus("1");
                });
                List<List<AccountSyncVo>> convert = ListUtil.convert(collect);
                convert.forEach(accountVos -> businessAccountSyncMapper.saveBatch(accountVos));
            }
            return AjaxResult.success();
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 校验用户
     * @param dataVo
     * @return
     */
    @Override
    public AjaxResult accountCheck(DataVo dataVo) {
        //解析用户
        List<AccountSyncVo> accountSyncVos = ParamParse.parseClass(dataVo.getData(), AccountSyncVo.class);
        AccountSyncVo accountSyncVo = accountSyncVos.get(0);
        BusinessAccountSync accountSync = businessAccountSyncMapper.selectByUsernameAndSystem(accountSyncVo);
        if (Objects.isNull(accountSync)){
            return AjaxResult.msg(10001,"校验不通过");
        }else{
            if (accountSync.getStatus().equals("0")) {
                return AjaxResult.msg(10001,"校验不通过");
            }
        }
        return AjaxResult.success("校验通过");
    }
}
