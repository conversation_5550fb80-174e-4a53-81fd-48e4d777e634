package com.rzl.workbenche.ops.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rzl.workbenche.common.annotation.Excel;
import com.rzl.workbenche.common.core.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 业务系统账户同步对象 business_account_sync
 *
 * <AUTHOR>
 * @date 2023-11-21
 */
public class AccountSyncVo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 业务系统 */
    @Excel(name = "业务系统")
    @NotBlank(message = "业务系统不能为空")
    private String system;

    /** 账户 */
    @Excel(name = "账户")
    @NotBlank(message = "账户不能为空")
    private String username;

    /** 电话 */
    @Excel(name = "电话")
    private String phone;

    /** 状态 (0:禁用  1：正常)*/
    @Excel(name = "状态")
    private String status;

    /** 同步时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "同步时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date syncTime;

    /** 修改人 */
    @Excel(name = "修改人")
    private String updateUser;
    /**
     * 类型 用户新增：1  用户删除 ：0
     */
    @NotBlank(message = "类型不能为空")
    private String type;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setSystem(String system)
    {
        this.system = system;
    }

    public String getSystem()
    {
        return system;
    }
    public void setUsername(String username)
    {
        this.username = username;
    }

    public String getUsername()
    {
        return username;
    }
    public void setPhone(String phone)
    {
        this.phone = phone;
    }

    public String getPhone()
    {
        return phone;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setSyncTime(Date syncTime)
    {
        this.syncTime = syncTime;
    }

    public Date getSyncTime()
    {
        return syncTime;
    }
    public void setUpdateUser(String updateUser)
    {
        this.updateUser = updateUser;
    }

    public String getUpdateUser()
    {
        return updateUser;
    }
}
