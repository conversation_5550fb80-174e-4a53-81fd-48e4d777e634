# 项目相关配置
expense:
  # 名称
  name: Expense
  # 版本
  version: 3.8.5
  # 版权年份
  copyrightYear: 2023
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/expense/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/workbenche/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8088
  port: 10086
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
#    com.rzl.workbenche: debug
#    org.springframework: warn
    com: off
#    com.rzl.workbenche: debug

management:
  # 端点配置
  endpoints:
    web:
      exposure:
        include: 'prometheus'
  server:
    port: 8091
  metrics:
    tags:
      application: ${spring.application.name}
# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认20分钟）
    lockTime: 20

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: prod
#    active: dev
#    active: druid
  # 文件上传
  servlet:
     multipart:
       # 单个文件大小
       max-file-size:  10MB
       # 设置总上传的文件大小
       max-request-size:  20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true

  application:
    name: workbenche-admin
# token配置
token:
    # 令牌自定义标识
    header: Authorization
    # 令牌密钥
    secret: abcdefghijklmnopqrstuvwxyz
    # 令牌有效期（默认30分钟）
    expireTime: 30

# MyBatis配置
mybatis:
    # 搜索指定包别名
    typeAliasesPackage: com.rzl.workbenche.**.domain
    # 配置mapper的扫描，找到所有的mapper.xml映射文件
    mapperLocations: classpath*:mapper/**/*Mapper.xml
    # 加载全局的配置文件
    configLocation: classpath:mybatis/mybatis-config.xml
# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: false
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

api:
  user:
    url: /v1/api
  costSystem:
#    loginUrl: http://10.15.2.38:9550/#/login?token=
#    loginUrl: http://10.15.2.38:9550/analysisSys/login/ssoLogin?token=
#    loginUrl: http://10.15.33.60:9550/analysisSys/login/ssoLogin?token=
    loginUrl: http://10.15.33.60:9550/analysisSys/login/ssoLogin?token=
    publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC+VW+Y/nwpBRPl+7CkXqTVx20mQbV9TPzDy9/yBX0lnWUinM7MyxXRc0aP2ScvsxyW05b4TnvlUNImIFUnCgBHFKy1zziN6eOpzQ6WXVOONeMRa9Cqsak6n4i5LdjC+xQnUwx0zD71DiwLD4Nd9ZBed2ArSkbMbpq5x3GpaMJP7QIDAQAB
  lockSystem:
    # loginUrl: http://115.29.204.20/webs/btlocktest/?token=
#    loginUrl: http://59.110.125.88:9551/webs/btlocktest/?token=
    loginUrl: /smartLock/webs/btlocktest/?token=
    publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC5BCjq/0BdP0O02CcAJaxqrsYCYMpf5gdbL94R+C/Md+U7kYGYAhMpYaCi/LBA+bofobNMjbvKK273Qr71p2Krt3lah6asuV+9Gv8I6DiRfPcrHW+Q3CtBgM0zKRxmDLYjz8AsyAIhvrq3aeeifsOR+TBpvxeHdImwgWbMP0phMQIDAQAB

sign:
#  privateKey: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCGX/5VAGLNfI0mYsuxJcXZmUtnHE4+72repQjm7EzMR9a84MXUg7Mbm+1H2BUCi2t0UKMKSHNU9lQN4SG4vKv53JMTPIMPFBt/lZjl6dBXLKH+BuRm22Q2MpKPOK5MUP7CXRYgHDHDUyugD1iHBkVzXdVPW/P/noSIdn9NxzEHmazWJaQ5RNIBM+8IwDp9RarRu8l8Jhi+lPX94qYMjHtsT2AgHHkMGdqTlV93hozoiHh9ZAedah04shb/2JtVt/w7tHZ6glS+NdtPgSYWGVKN8LjsVmvYCTLzA+x+jayg3CrfwflCdDk4YloXZLBMRd5EjNKU1Xia86yTkKVNAV5jAgMBAAECggEAZLEH7wAUCXDcgDgl9H+ei1jgdMp0I5raVS4LxqapQaZPVkeEVqO9keG840obLEX25jkKg0kZC0Buadwancjbgab0msSMXL8vf+JEqXwUf05VsPnPKvgZVrd8tU++5DcJP/HrA3K9I2VuWklFiCB96F4W2pnH8/L/0xYgfNLXfZ5aOChoqlcQfCk9YzrbVH+dlGLLLOYkNthti4Gnt3Id3tio/jkrRPv4stlQGzlR6relvs+MPpQSJGGVN9V6mMRz0QADCQ8vJ+TopVU7MoT+4AZ5BiLCG6CVRRBaaZTcMUBDapIUe05dcAJl0TEpxyWbiX7+5Q13iFe3/Kp43kXMWQKBgQD7kI+XrcO/SDpg9k3ToQ0ffXWI9OWejEP0SPq857W0RKxf43fXGD8HOdv86X0XQ1gub4sPhdjfLhxBFPl93vPV82dDK9DVojYlzNTmDZyQz/arkJk+33+79tD7ZuNBOKFtWKrkuPykI5Et4xnWeET2rgyriE0PgKI4cgt3bU6kNQKBgQCIvn708Tg2o2JrfhODh4BPTClFSg+vHi+kYWzypCouvuLVad/hOyfOWNfWxK52aU91wFs5MKUa3MVGaNe7PHBavMeD/KrVVImeV9Qm7d00JjzwRb81GdgAti5uHRXvybqinUi46xmnDa0gXNqgvCMLp8X6Kmqe+/j9Nl/nevCbNwKBgAFa4PsfK3u/Rtk42mh2aJyvz09Cim4VMVSChB86XEcxS4BkuEOHVOeUDzRIiazzZukxHp2hye5B0FMlasabydJwMjExbjmIWyRO+yKWFi7Nf/WinMCy8XwwsyV3g2fZhz0QijMbL/kuW9zUaVI3aZ+uhlDyNgAMYFNPgfJNu2u1AoGAFi13H7jDmjDzdGWVOp7OU44Rn3g8hPVLWsimR9ASO1SgK2apdbkHYrMMBnwNo4y+42UYJnVaQboOkLXOT0KlIkI9xY1EE6dLTpAGC25Gkv+koH9fXHhdjPf/HcK/3Q0FpPwHYF5Y4bwZGQeWOb1pnalPNZyNgE1ze3XHJh7NSQ8CgYAXkL95eYTM8UAFhzaFs6ySI29HyIRFCPlpNaIvBxha2m5HtVBhC42ya0BQqtsbk8rAMHBFR/C+xYU6/HfDQjwlipwYOwKvxW1FCUNTvSHeH2Sd3AwQJkT6R7JmLV+AcOs7tR/YjFGqdb53YnfCsS1MWEAYLpb2XLoxDjMsNjGbVw==
  privateKey: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAKEa8pkbupbZpCxhgAolJ0IWd+tkFreII9yaL/FUx5LVQlbzQNpw/RtVjJ2YM7E0a0Ch9G71sFs0svMeyuC7nImZF+i1GXXnGYjSBEK5Wx/FBz8IdVRssUa0BNU4u5j497QJrrMUbheHsE7/vZ79mBTBH2PsctZB88aGH5/7I/bfAgMBAAECgYB+VMJN25eaePorLx2XVwEM6s+Ij0jdDG2KrIHRzXheJMrLoh6F7LodeB0260YFvCQqX5VseU6chpuY19mhFp0tbGimorZDM8EvUkPLihZEPTv831Zq8/apWHzF4/kUcYHaSMOvSXAj7VRP8qUiunFlplG6Z7hD5BH2hZaS8j99yQJBAORB8zt8HmEkwBA2qUe38Vsu3dmkhlif0D/qztqnlAHBUyIVKKP75xcCnHVNqCh7PmPxFKZnLf9s0EP7cecHFRsCQQC0r5tTFXgckofFsCPob2rzO0j90mwpaRghMLu3nUUyuA0DuZnWEJixoo6+mOytbhgBapTJfKH8kaOftjewSHWNAkAQh54frqtciZbHFc5IfU+jNM+oFTwNavVfy5dTSlNzlRZ6H2IkDff8OJov/IGy/MnV3v2J12sDVlP2uFzVSDQFAkEAjj4EsFahdRTh7/4ndo9oCc2tO6zQ25TRmydrUDRuSmxcSodtlPkBzC3l5CQthqa6HTtToH8OYvAgeNYRZyTT7QJAU9Oz6f6CuIaDtjMm/AludsNHo9Wb3K+4U0XW2JcTtU6slYsX0NB2ni5NEjg75qbDefd78fyHHbrjxubC26zCPg==
  publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQChGvKZG7qW2aQsYYAKJSdCFnfrZBa3iCPcmi/xVMeS1UJW80DacP0bVYydmDOxNGtAofRu9bBbNLLzHsrgu5yJmRfotRl15xmI0gRCuVsfxQc/CHVUbLFGtATVOLuY+Pe0Ca6zFG4Xh7BO/72e/ZgUwR9j7HLWQfPGhh+f+yP23wIDAQAB


