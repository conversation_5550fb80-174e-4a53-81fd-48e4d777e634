package com.rzl.workbenche.web.controller.ops;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rzl.workbenche.common.annotation.RepeatSubmit;
import com.rzl.workbenche.ops.domain.dto.SystemGuideDto;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rzl.workbenche.common.annotation.Log;
import com.rzl.workbenche.common.core.controller.BaseController;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.enums.BusinessType;
import com.rzl.workbenche.ops.domain.OpsSystemGuide;
import com.rzl.workbenche.ops.service.IOpsSystemGuideService;
import com.rzl.workbenche.common.utils.poi.ExcelUtil;
import com.rzl.workbenche.common.core.page.TableDataInfo;

/**
 * 无感接入系统向导配置Controller
 *
 * <AUTHOR>
 * @date 2023-11-09
 */
@RestController
@RequestMapping("/ops/openApi")
public class OpsSystemGuideController extends BaseController
{
    @Autowired
    private IOpsSystemGuideService opsSystemGuideService;

    /**
     * 查询无感接入系统向导配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:openapi:index')")
    @GetMapping("/list")
    public TableDataInfo list(OpsSystemGuide opsSystemGuide)
    {
        startPage();
        List<OpsSystemGuide> list = opsSystemGuideService.selectOpsSystemGuideList(opsSystemGuide);
        return getDataTable(list);
    }

    /**
     * 导出无感接入系统向导配置列表
     */
    @PreAuthorize("@ss.hasPermi('ops:guide:export')")
    @Log(title = "无感接入系统向导配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OpsSystemGuide opsSystemGuide)
    {
        List<OpsSystemGuide> list = opsSystemGuideService.selectOpsSystemGuideList(opsSystemGuide);
        ExcelUtil<OpsSystemGuide> util = new ExcelUtil<>(OpsSystemGuide.class);
        util.exportExcel(response, list, "无感接入系统向导配置数据");
    }

    /**
     * 获取无感接入系统向导配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('ops:guide:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(opsSystemGuideService.selectOpsSystemGuideById(id));
    }

    /**
     * 新增Api
     */
    @PreAuthorize("@ss.hasPermi('ops:guide:add')")
    @Log(title = "无感接入系统向导配置", businessType = BusinessType.INSERT)
    @PostMapping("/api/add")
    public AjaxResult add(@RequestBody OpsSystemGuide opsSystemGuide)
    {
        return opsSystemGuideService.insertOpsSystemGuide(opsSystemGuide);
    }
    /**
     * 新增无感接入系统向导配置
     */
    @PreAuthorize("@ss.hasPermi('system:openapi:add')")
    @RepeatSubmit(message = "操作过于频繁")
    @Log(title = "无感接入系统向导配置", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public AjaxResult save(@Validated @RequestBody SystemGuideDto guideDto)
    {

        return opsSystemGuideService.saveOpsSystemGuide(guideDto);
    }

    /**
     * 修改无感接入系统向导配置
     */
    @PreAuthorize("@ss.hasPermi('ops:guide:edit')")
    @Log(title = "无感接入系统向导配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit( @Validated @RequestBody OpsSystemGuide opsSystemGuide)
    {
        return toAjax(opsSystemGuideService.updateOpsSystemGuide(opsSystemGuide));
    }

    /**
     * 删除无感接入系统向导配置
     */
    @PreAuthorize("@ss.hasPermi('ops:guide:remove')")
    @Log(title = "无感接入系统向导配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id)
    {
        return toAjax(opsSystemGuideService.deleteOpsSystemGuideById(id));
    }
}
