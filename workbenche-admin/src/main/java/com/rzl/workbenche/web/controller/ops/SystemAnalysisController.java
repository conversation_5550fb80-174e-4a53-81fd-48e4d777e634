package com.rzl.workbenche.web.controller.ops;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rzl.workbenche.ops.domain.SystemAnalysis;
import com.rzl.workbenche.ops.service.ISystemAnalysisService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rzl.workbenche.common.annotation.Log;
import com.rzl.workbenche.common.core.controller.BaseController;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.enums.BusinessType;

import com.rzl.workbenche.common.utils.poi.ExcelUtil;
import com.rzl.workbenche.common.core.page.TableDataInfo;

/**
 * 系统情况分析Controller
 *
 * <AUTHOR>
 * @date 2023-08-24
 */
@RestController
@RequestMapping("/ops/system/analysis")
public class SystemAnalysisController extends BaseController
{
    @Autowired
    private ISystemAnalysisService systemAnalysisService;

    /**
     * 查询系统情况分析列表
     */
    @PreAuthorize("@ss.hasPermi('ops:system:analyse:list')")
    @GetMapping("/list")
    public TableDataInfo list(SystemAnalysis systemAnalysis)
    {
        startPage();
        List<SystemAnalysis> list = systemAnalysisService.selectSystemAnalysisList(systemAnalysis);
        return getDataTable(list);
    }

    /**
     * 导出系统情况分析列表
     */
    @PreAuthorize("@ss.hasPermi('ops:system:analyse:export')")
    @Log(title = "系统情况分析", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SystemAnalysis systemAnalysis)
    {
        List<SystemAnalysis> list = systemAnalysisService.selectSystemAnalysisList(systemAnalysis);
        ExcelUtil<SystemAnalysis> util = new ExcelUtil<>(SystemAnalysis.class);
        util.exportExcel(response, list, "系统情况分析数据");
    }

    /**
     * 获取系统情况分析详细信息
     */
    @PreAuthorize("@ss.hasPermi('ops:system:analyse:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(systemAnalysisService.selectSystemAnalysisById(id));
    }

    /**
     * 新增系统情况分析
     */
    @PreAuthorize("@ss.hasPermi('ops:system:analyse:add')")
    @Log(title = "系统情况分析", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SystemAnalysis systemAnalysis)
    {
        return toAjax(systemAnalysisService.insertSystemAnalysis(systemAnalysis));
    }

    /**
     * 修改系统情况分析
     */
    @PreAuthorize("@ss.hasPermi('ops:system:analyse:edit')")
    @Log(title = "系统情况分析", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SystemAnalysis systemAnalysis)
    {
        return toAjax(systemAnalysisService.updateSystemAnalysis(systemAnalysis));
    }

    /**
     * 删除系统情况分析
     */
    @PreAuthorize("@ss.hasPermi('ops:system:analyse:remove')")
    @Log(title = "系统情况分析", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(systemAnalysisService.deleteSystemAnalysisByIds(ids));
    }
}
