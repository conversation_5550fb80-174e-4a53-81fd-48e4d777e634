package com.rzl.workbenche.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rzl.workbenche.common.annotation.Log;
import com.rzl.workbenche.common.core.controller.BaseController;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.enums.BusinessType;
import com.rzl.workbenche.system.domain.SysEnumPwd;
import com.rzl.workbenche.system.service.ISysEnumPwdService;
import com.rzl.workbenche.common.utils.poi.ExcelUtil;
import com.rzl.workbenche.common.core.page.TableDataInfo;

/**
 * 密码管理Controller
 *
 * <AUTHOR>
 * @date 2024-06-13
 */
@RestController
@RequestMapping("/system/pwd")
public class SysEnumPwdController extends BaseController
{
    @Autowired
    private ISysEnumPwdService sysEnumPwdService;

    /**
     * 查询密码管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:pwd:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysEnumPwd sysEnumPwd)
    {
        startPage();
        List<SysEnumPwd> list = sysEnumPwdService.selectSysEnumPwdList(sysEnumPwd);
        return getDataTable(list);
    }

    /**
     * 导出密码管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:pwd:export')")
    @Log(title = "密码管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysEnumPwd sysEnumPwd)
    {
        List<SysEnumPwd> list = sysEnumPwdService.selectSysEnumPwdList(sysEnumPwd);
        ExcelUtil<SysEnumPwd> util = new ExcelUtil<SysEnumPwd>(SysEnumPwd.class);
        util.exportExcel(response, list, "密码管理数据");
    }

    /**
     * 获取密码管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:pwd:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sysEnumPwdService.selectSysEnumPwdById(id));
    }

    /**
     * 新增密码管理
     */
    @PreAuthorize("@ss.hasPermi('system:pwd:add')")
    @Log(title = "密码管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysEnumPwd sysEnumPwd)
    {
        return toAjax(sysEnumPwdService.insertSysEnumPwd(sysEnumPwd));
    }

    /**
     * 修改密码管理
     */
    @PreAuthorize("@ss.hasPermi('system:pwd:edit')")
    @Log(title = "密码管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysEnumPwd sysEnumPwd)
    {
        return toAjax(sysEnumPwdService.updateSysEnumPwd(sysEnumPwd));
    }

    /**
     * 删除密码管理
     */
    @PreAuthorize("@ss.hasPermi('system:pwd:remove')")
    @Log(title = "密码管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sysEnumPwdService.deleteSysEnumPwdByIds(ids));
    }
}
