package com.rzl.workbenche.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.rzl.workbenche.common.constant.SysConstants;
import com.rzl.workbenche.common.core.domain.entity.SysRegion;
import com.rzl.workbenche.common.exception.ServiceException;
import com.rzl.workbenche.ops.domain.IndexBoard;
import com.rzl.workbenche.ops.domain.dto.IndexBoardDto;
import com.rzl.workbenche.ops.service.IIndexBoardService;
import com.rzl.workbenche.system.domain.SysRoleIndex;
import com.rzl.workbenche.system.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rzl.workbenche.common.annotation.Log;
import com.rzl.workbenche.common.constant.UserConstants;
import com.rzl.workbenche.common.core.controller.BaseController;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.core.domain.entity.SysRole;
import com.rzl.workbenche.common.core.domain.entity.SysUser;
import com.rzl.workbenche.common.core.domain.model.LoginUser;
import com.rzl.workbenche.common.core.page.TableDataInfo;
import com.rzl.workbenche.common.enums.BusinessType;
import com.rzl.workbenche.common.utils.StrUtils;
import com.rzl.workbenche.common.utils.poi.ExcelUtil;
import com.rzl.workbenche.framework.web.service.SysPermissionService;
import com.rzl.workbenche.framework.web.service.TokenService;
import com.rzl.workbenche.system.domain.SysUserRole;

/**
 * 角色信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/role")
public class SysRoleController extends BaseController
{
    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysRegionService regionService;

    @Autowired
    private IIndexBoardService iIndexBoardService;

    @Autowired
    private ISysRoleIndexService roleIndexService;

    @PreAuthorize("@ss.hasPermi('system:role:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysRole role)
    {
        startPage();
        List<SysRole> list = roleService.selectRoleList(role);
        return getDataTable(list);
    }

    @Log(title = "角色管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:role:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysRole role)
    {
        List<SysRole> list = roleService.selectRoleList(role);
        ExcelUtil<SysRole> util = new ExcelUtil<>(SysRole.class);
        util.exportExcel(response, list, "角色数据");
    }

    /**
     * 根据角色编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:role:query')")
    @GetMapping(value = "/{roleId}")
    public AjaxResult getInfo(@PathVariable Long roleId)
    {
        roleService.checkRoleDataScope(roleId);
        return success(roleService.selectRoleById(roleId));
    }

    /**
     * 新增角色
     */
    @PreAuthorize("@ss.hasPermi('system:role:add')")
    @Log(title = "角色管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysRole role)
    {
        if (StrUtils.isNotEmpty(role.getRemark()) && role.getRemark().length()>500){
            throw new ServiceException("备注长度超过限制");
        }
        if (UserConstants.NOT_UNIQUE.equals(roleService.checkRoleNameUnique(role)))
        {
            return error(SysConstants.ROLE_ADD + role.getRoleName() + SysConstants.ROLE_NAME_DETAIL);
        }
        else if (UserConstants.NOT_UNIQUE.equals(roleService.checkRoleKeyUnique(role)))
        {
            return error(SysConstants.ROLE_ADD + role.getRoleName() + SysConstants.ROLE_CHAT_DETAIL);
        }
        role.setCreateBy(getUsername());
        return toAjax(roleService.insertRole(role));

    }
    /**
     * 向导新增角色
     */
    @PreAuthorize("@ss.hasPermi('system:role:add')")
    @Log(title = "角色管理", businessType = BusinessType.INSERT)
    @PostMapping("/guide")
    public AjaxResult guideRole(@Validated @RequestBody SysRole role){
        if (UserConstants.NOT_UNIQUE.equals(roleService.checkRoleNameUnique(role)))
        {
            return error(SysConstants.ROLE_ADD + role.getRoleName() + SysConstants.ROLE_NAME_DETAIL);
        }
        else if (UserConstants.NOT_UNIQUE.equals(roleService.checkRoleKeyUnique(role)))
        {
            return error(SysConstants.ROLE_ADD + role.getRoleName() + SysConstants.ROLE_CHAT_DETAIL);
        }
        role.setCreateBy(getUsername());
        return roleService.insertGuideRole(role);
    }
    /**
     * 修改保存角色
     */
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysRole role)
    {
        if (StrUtils.isNotEmpty(role.getRemark()) && role.getRemark().length()>500){
            throw new ServiceException("备注长度超过限制");
        }
        roleService.checkRoleAllowed(role);
        roleService.checkRoleDataScope(role.getRoleId());
        if (UserConstants.NOT_UNIQUE.equals(roleService.checkRoleNameUnique(role)))
        {
            return error(SysConstants.ROLE_EDIT + role.getRoleName() + SysConstants.ROLE_NAME_DETAIL);
        }
        else if (UserConstants.NOT_UNIQUE.equals(roleService.checkRoleKeyUnique(role)))
        {
            return error(SysConstants.ROLE_EDIT + role.getRoleName() + SysConstants.ROLE_CHAT_DETAIL);
        }
        role.setUpdateBy(getUsername());

        if (roleService.updateRole(role) > 0)
        {
            // 更新缓存用户权限
            LoginUser loginUser = getLoginUser();
            if (StrUtils.isNotNull(loginUser.getUser()) && !loginUser.getUser().isAdmin())
            {
                loginUser.setPermissions(permissionService.getMenuPermission(loginUser.getUser()));
                loginUser.setUser(userService.selectUserByUserName(loginUser.getUser().getUserName()));
                tokenService.setLoginUser(loginUser);
            }
            return success();
        }
        return error(SysConstants.ROLE_EDIT + role.getRoleName() + "'失败，请联系管理员");
    }
    /**
     * 配置向导修改保存角色
     */
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping("/guide")
    public AjaxResult guideEdit(@Validated @RequestBody SysRole role)
    {
        roleService.checkRoleAllowed(role);
        roleService.checkRoleDataScope(role.getRoleId());
        if (UserConstants.NOT_UNIQUE.equals(roleService.checkRoleNameUnique(role)))
        {
            return error(SysConstants.ROLE_EDIT + role.getRoleName() + SysConstants.ROLE_NAME_DETAIL);
        }
        else if (UserConstants.NOT_UNIQUE.equals(roleService.checkRoleKeyUnique(role)))
        {
            return error(SysConstants.ROLE_EDIT + role.getRoleName() + SysConstants.ROLE_CHAT_DETAIL);
        }
        role.setUpdateBy(getUsername());

        if (roleService.updateGuideRole(role) > 0)
        {
            // 更新缓存用户权限
            LoginUser loginUser = getLoginUser();
            if (StrUtils.isNotNull(loginUser.getUser()) && !loginUser.getUser().isAdmin())
            {
                loginUser.setPermissions(permissionService.getMenuPermission(loginUser.getUser()));
                loginUser.setUser(userService.selectUserByUserName(loginUser.getUser().getUserName()));
                tokenService.setLoginUser(loginUser);
            }
            return success();
        }
        return error(SysConstants.ROLE_EDIT + role.getRoleName() + "'失败，请联系管理员");
    }
    /**
     * 修改保存数据权限
     */
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping("/dataScope")
    public AjaxResult dataScope(@RequestBody SysRole role)
    {
        roleService.checkRoleAllowed(role);
        roleService.checkRoleDataScope(role.getRoleId());
        return toAjax(roleService.authDataScope(role));
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysRole role)
    {
        roleService.checkRoleAllowed(role);
        roleService.checkRoleDataScope(role.getRoleId());
        role.setUpdateBy(getUsername());
        return toAjax(roleService.updateRoleStatus(role));
    }

    /**
     * 删除角色
     */
    @PreAuthorize("@ss.hasPermi('system:role:remove')")
    @Log(title = "角色管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{roleIds}")
    public AjaxResult remove(@PathVariable Long[] roleIds)
    {
        return toAjax(roleService.deleteRoleByIds(roleIds));
    }

    /**
     * 获取角色选择框列表
     */
    @PreAuthorize("@ss.hasPermi('system:role:query')")
    @GetMapping("/optionselect")
    public AjaxResult optionselect()
    {
        return success(roleService.selectRoleAll());
    }

    /**
     * 查询已分配用户角色列表
     */
    @PreAuthorize("@ss.hasPermi('system:role:list')")
    @GetMapping("/authUser/allocatedList")
    public TableDataInfo allocatedList(SysUser user)
    {
        startPage();
        List<SysUser> list = userService.selectAllocatedList(user);
        return getDataTable(list);
    }

    /**
     * 查询已指标角色列表
     */
    @PreAuthorize("@ss.hasPermi('system:role:list')")
    @GetMapping("/index/allocatedList")
    public TableDataInfo indexAllocatedList(IndexBoardDto indexBoardDto)
    {
        startPage();
        List<IndexBoard> list = iIndexBoardService.selectIndexAllocatedList(indexBoardDto);
        return getDataTable(list);
    }

    /**
     * 查询未分配用户角色列表
     */
    @PreAuthorize("@ss.hasPermi('system:role:list')")
    @GetMapping("/authUser/unallocatedList")
    public TableDataInfo unallocatedList(SysUser user)
    {
        startPage();
        List<SysUser> list = userService.selectUnallocatedList(user);
        return getDataTable(list);
    }
    /**
     * 查询未分配指标角色列表
     */
    @PreAuthorize("@ss.hasPermi('system:role:list')")
    @GetMapping("/index/unallocatedList")
    public TableDataInfo indexUnallocatedList(IndexBoardDto indexBoardDto)
    {
        startPage();
        List<IndexBoard> list = iIndexBoardService.selectIndexUnallocatedList(indexBoardDto);
        return getDataTable(list);
    }
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("/index/selectAll")
    public AjaxResult selectIndexAuthUserAll(Long roleId, Long[] indexIds)
    {
        roleService.checkRoleDataScope(roleId);
        return toAjax(roleService.insertAllocationIndex(roleId, indexIds));
    }
    /**
     * 取消授权用户
     */
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/cancel")
    public AjaxResult cancelAuthUser(@RequestBody SysUserRole userRole)
    {
        return toAjax(roleService.deleteAuthUser(userRole));
    }
    /**
     * 取消指标授权用户
     */
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("/index/cancel")
    public AjaxResult cancelIndexAuthUser(@RequestBody SysRoleIndex roleIndex)
    {
        return toAjax(roleService.deleteIndex(roleIndex));
    }
    /**
     * 批量取消指标授权
     */
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("/index/cancelAll")
    public AjaxResult cancelIndexAuthUserAll(Long roleId, Long[] indexIds)
    {
        return toAjax(roleService.deleteIndexAuthUsers(roleId, indexIds));
    }
    /**
     * 批量取消授权用户
     */
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/cancelAll")
    public AjaxResult cancelAuthUserAll(Long roleId, Long[] userIds)
    {
        return toAjax(roleService.deleteAuthUsers(roleId, userIds));
    }
    /**
     * 批量选择用户授权
     */
    @PreAuthorize("@ss.hasPermi('system:role:edit')")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/selectAll")
    public AjaxResult selectAuthUserAll(Long roleId, Long[] userIds)
    {
        roleService.checkRoleDataScope(roleId);
        return toAjax(roleService.insertAuthUsers(roleId, userIds));
    }

    /**
     * 获取对应角色地市树列表
     */
    @PreAuthorize("@ss.hasPermi('system:role:query')")
    @GetMapping(value = "/regionTree/{roleId}")
    public AjaxResult deptTree(@PathVariable("roleId") Long roleId)
    {
        AjaxResult ajax = AjaxResult.success();
        ajax.put("checkedKeys", regionService.selectRegionListByRoleId(roleId));
        ajax.put("regions", regionService.selectRegionTreeList(new SysRegion()));
        return ajax;
    }
    /**
     * 获取对应角色地市树列表
     */
    @PreAuthorize("@ss.hasPermi('system:role:query')")
    @GetMapping(value = "/regionTree")
    public AjaxResult deptTree()
    {
        AjaxResult ajax = AjaxResult.success();
        ajax.put("regions", regionService.selectRegionTreeList(new SysRegion()));
        return ajax;
    }
    /**
     * 角色向导查看详情
     */
    @GetMapping("/board/{roleId}")
    public AjaxResult getRoleDetail(@PathVariable("roleId") Long roleId){
        AjaxResult ajax = iIndexBoardService.getTree();
        ajax.put("checkedKeys", roleIndexService.selectSysRoleIndexByRoleId(roleId).stream().map(SysRoleIndex::getIndexId).collect(Collectors.toList()));
        return ajax;
    }

}
