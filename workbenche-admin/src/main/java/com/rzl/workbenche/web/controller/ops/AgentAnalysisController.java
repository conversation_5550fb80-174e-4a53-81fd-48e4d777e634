package com.rzl.workbenche.web.controller.ops;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rzl.workbenche.ops.domain.AgentAnalysis;
import com.rzl.workbenche.ops.service.IAgentAnalysisService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rzl.workbenche.common.annotation.Log;
import com.rzl.workbenche.common.core.controller.BaseController;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.enums.BusinessType;

import com.rzl.workbenche.common.utils.poi.ExcelUtil;
import com.rzl.workbenche.common.core.page.TableDataInfo;

/**
 * 待办效率分析Controller
 *
 * <AUTHOR>
 * @date 2023-08-24
 */
@RestController
@RequestMapping("/ops/agent/analysis")
public class AgentAnalysisController extends BaseController {
    @Autowired
    private IAgentAnalysisService analysisService;

    /**
     * 查询待办效率分析列表
     */
    @PreAuthorize("@ss.hasPermi('ops:agent:analyse:list')")
    @GetMapping("/list")
    public TableDataInfo list(AgentAnalysis agentAnalysis) {
        startPage();
        List<AgentAnalysis> list = analysisService.selectAgentAnalysisList(agentAnalysis);
        return getDataTable(list);
    }

    /**
     * 导出待办效率分析列表
     */
    @PreAuthorize("@ss.hasPermi('ops:agent:analyse:export')")
    @Log(title = "待办效率分析", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AgentAnalysis agentAnalysis) {
        List<AgentAnalysis> list = analysisService.selectAgentAnalysisList(agentAnalysis);
        ExcelUtil<AgentAnalysis> util = new ExcelUtil<>(AgentAnalysis.class);
        util.exportExcel(response, list, "待办效率分析数据");
    }

    /**
     * 获取待办效率分析详细信息
     */
    @PreAuthorize("@ss.hasPermi('ops:agent:analyse:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(analysisService.selectAgentAnalysisById(id));
    }

    /**
     * 新增待办效率分析
     */
    @PreAuthorize("@ss.hasPermi('ops:agent:analyse:add')")
    @Log(title = "待办效率分析", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AgentAnalysis agentAnalysis) {
        return toAjax(analysisService.insertAgentAnalysis(agentAnalysis));
    }

    /**
     * 修改待办效率分析
     */
    @PreAuthorize("@ss.hasPermi('ops:agent:analyse:edit')")
    @Log(title = "待办效率分析", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AgentAnalysis agentAnalysis) {
        return toAjax(analysisService.updateAgentAnalysis(agentAnalysis));
    }

    /**
     * 删除待办效率分析
     */
    @PreAuthorize("@ss.hasPermi('ops:agent:analyse:remove')")
    @Log(title = "待办效率分析", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(analysisService.deleteAgentAnalysisByIds(ids));
    }
}
