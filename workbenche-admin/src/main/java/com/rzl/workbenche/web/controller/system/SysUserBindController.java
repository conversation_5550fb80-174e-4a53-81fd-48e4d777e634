package com.rzl.workbenche.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rzl.workbenche.system.domain.dto.SysUserBindDto;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rzl.workbenche.common.annotation.Log;
import com.rzl.workbenche.common.core.controller.BaseController;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.enums.BusinessType;
import com.rzl.workbenche.system.domain.SysUserBind;
import com.rzl.workbenche.system.service.ISysUserBindService;
import com.rzl.workbenche.common.utils.poi.ExcelUtil;
import com.rzl.workbenche.common.core.page.TableDataInfo;

/**
 * 用户系统绑定Controller
 *
 * <AUTHOR>
 * @date 2023-09-12
 */
@RestController
@RequestMapping("/system/bind")
public class SysUserBindController extends BaseController
{
    @Autowired
    private ISysUserBindService sysUserBindService;

    /**
     * 查询用户系统绑定列表
     */
    @PreAuthorize("@ss.hasPermi('system:user:account:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysUserBind sysUserBind)
    {
        startPage();
        List<SysUserBind> list = sysUserBindService.selectSysUserBindList(sysUserBind);
        return getDataTable(list);
    }



    /**
     * 获取用户系统绑定详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:user:account:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sysUserBindService.selectSysUserBindById(id));
    }

    /**
     * 导出用户系统绑定列表
     */
    @Log(title = "用户系统绑定", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:user:account:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysUserBind sysUserBind)
    {
        List<SysUserBind> list = sysUserBindService.selectSysUserBindList(sysUserBind);
        ExcelUtil<SysUserBind> util = new ExcelUtil<>(SysUserBind.class);
        util.exportExcel(response, list, "用户系统绑定数据");
    }

    /**
     * 新增用户系统绑定
     */
    @PreAuthorize("@ss.hasPermi('system:user:account:add')")
    @Log(title = "用户系统绑定", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysUserBind sysUserBind)
    {
        return toAjax(sysUserBindService.insertSysUserBind(sysUserBind));
    }

    /**
     * 用户绑定
     */
    @PreAuthorize("@ss.hasPermi('system:user:account:bindUser')")
    @PostMapping("/bindUser")
    public AjaxResult bind( @Validated @RequestBody SysUserBindDto sysUserBindDto){
        return sysUserBindService.bind(sysUserBindDto);
    }

    /**
     * 用户解绑
     */
    @PreAuthorize("@ss.hasPermi('system:user:account:unBind')")
    @PostMapping("/unBind")
    public AjaxResult unBind(@RequestBody SysUserBindDto userBindDto){
        return sysUserBindService.unBind(userBindDto);
    }
    /**
     * 修改用户系统绑定
     */
    @PreAuthorize("@ss.hasPermi('system:user:account:edit')")
    @Log(title = "用户系统绑定", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysUserBind sysUserBind)
    {
        return toAjax(sysUserBindService.updateSysUserBind(sysUserBind));
    }

    /**
     * 删除用户系统绑定
     */
    @PreAuthorize("@ss.hasPermi('system:user:account:remove')")
    @Log(title = "用户系统绑定", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id)
    {
        return toAjax(sysUserBindService.deleteSysUserBindById(id));
    }
    /**
     * 根据主账号查询
     */
    @GetMapping("/query/{userId}")
    public AjaxResult findByUserId(@PathVariable("userId") Long userId){
        return sysUserBindService.findByUserId(userId);
    }
    /**
     * 新增和校验
     * @param bind
     * @return
     */
    @PostMapping("/check")
    public AjaxResult check(@RequestBody SysUserBind bind)
    {
        return sysUserBindService.checkUserBind(bind);

    }
}
