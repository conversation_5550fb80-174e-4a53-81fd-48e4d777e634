package com.rzl.workbenche.web.controller.ops.api;

import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.ops.domain.vo.TokenApiVo;
import com.rzl.workbenche.ops.service.api.IApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/ops/api")
public class ApiController {

    @Autowired
    private IApiService apiService;
    /**
     * 获取网络成本分析系统token
     */
    @PostMapping("/getToken")
    public AjaxResult getToken(@RequestBody TokenApiVo apiVo){
        return apiService.getUrl(apiVo.getSystem(),apiVo.getToken());
    }

    /**
     * 获取三方菜单
     */
    @GetMapping("/getMenu")
    public AjaxResult getMeun(){
        return apiService.getMeun();
    }

    /**
     * 保存菜单
     */
    @PostMapping("/menu/query")
    public AjaxResult query(@RequestParam("id") Long id){
        return apiService.queryMenu(id);
    }
    /**
     * 业务菜单刷新
     */
    @GetMapping("/menu/refresh/{system}")
    public AjaxResult refresh(@PathVariable("system") String system){
        return apiService.refresh(system);
    }
}
