package com.rzl.workbenche.web.controller.ops.api;

import com.rzl.workbenche.common.annotation.CheckSign;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.ops.domain.vo.DataVo;
import com.rzl.workbenche.ops.mapper.OpsBaseStationEnergyConsumMapper;
import com.rzl.workbenche.ops.service.*;
import com.rzl.workbenche.ops.service.index.IIndexBoardStatisticsService;
import com.rzl.workbenche.ops.service.index.IIndexLockDeviceService;
import com.rzl.workbenche.ops.service.index.IIndexLockService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 开放三方接口
 */
@RestController
@RequestMapping("/v1/api")
public class TripartiteApiV1Controller {

    @Autowired
    private IOpsAgentService opsAgentService;

    @Autowired
    private IAlarmService alarmService;

    @Autowired
    private IIndexBoardStatisticsService statisticsService;

    @Autowired
    private IIndexLockService lockService;

    @Autowired
    private IIndexLockDeviceService deviceService;

    @Autowired
    private IRptEleCostAverageService averageService;

    @Autowired
    private IBusinessAccountSyncService accountSyncService;

    @Autowired
    private IOpsBaseStationEnergyConsumService consumService;

    @CheckSign("待办采集")
    @PostMapping("/agent")
    public AjaxResult agent(@Validated @RequestBody DataVo dataVo){

        return opsAgentService.saveBatchAgent(dataVo);
    }
    @CheckSign("待办状态采集")
    @PostMapping("/agent/updateAgentStatus")
    public AjaxResult updateAgentStatus(@Validated @RequestBody DataVo dataVo){
        return opsAgentService.updateAgentStatus(dataVo);
    }
    @CheckSign("告警采集")
    @PostMapping("/alarm")
    public AjaxResult alarm(@RequestBody DataVo dataVo)
    {
        return alarmService.saveBatchAlarm(dataVo);
    }
    @CheckSign("告警状态采集")
    @PostMapping("/alarm/updateAlarmStatus")
    public AjaxResult updateAlarmStatus(@Validated @RequestBody DataVo dataVo){
        return alarmService.updateAlarmStatus(dataVo);
    }
    @CheckSign("指标采集")
    @PostMapping("/qualityData")
    public AjaxResult qualityData(@Validated @RequestBody DataVo dataVo){
        return statisticsService.saveBatchBoardStatistics(dataVo);
    }
    @CheckSign("智能锁指标采集")
    @PostMapping("/lock")
    public AjaxResult lock(@Validated @RequestBody DataVo dataVo){
        return lockService.saveBatchIndexLock(dataVo);
    }

    @CheckSign("智能锁设施指标采集")
    @PostMapping("/lock/device")
    public AjaxResult lockDevice(@Validated @RequestBody DataVo dataVo){
        return deviceService.saveBatchIndexLockDevice(dataVo);
    }

    @CheckSign("成本分析平均用电数据采集")
    @PostMapping("/costs/eleCostAvg")
    public AjaxResult eleCostAvg(@Validated @RequestBody DataVo dataVo){
        return averageService.saveBatchRptEleCostAverage(dataVo);
    }

    @CheckSign("账号校验")
    @PostMapping("/user/check")
    public AjaxResult accountCheck(@RequestBody DataVo dataVo){
        return accountSyncService.accountCheck(dataVo);

    }
    @CheckSign("账号同步")
    @PostMapping("/user/sync")
    public AjaxResult accountSync(@RequestBody DataVo dataVo){

        return accountSyncService.accountSync(dataVo);
    }

    @CheckSign("基站能耗信息采集")
    @PostMapping("/base/energy/collect")
    public AjaxResult energyCollect(@RequestBody DataVo dataVo){
        return consumService.energyCollect(dataVo);
    }
}
