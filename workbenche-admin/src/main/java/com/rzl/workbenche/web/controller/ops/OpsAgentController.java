package com.rzl.workbenche.web.controller.ops;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import com.rzl.workbenche.ops.domain.OpsAgent;
import com.rzl.workbenche.ops.domain.dto.BatchDto;
import com.rzl.workbenche.ops.domain.export.OpsAgentExport;
import com.rzl.workbenche.ops.service.IOpsAgentService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.rzl.workbenche.common.annotation.Log;
import com.rzl.workbenche.common.core.controller.BaseController;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.enums.BusinessType;
import com.rzl.workbenche.common.utils.poi.ExcelUtil;
import com.rzl.workbenche.common.core.page.TableDataInfo;

/**
 * 运维待办管理Controller
 *
 * <AUTHOR>
 * @date 2023-08-23
 */
@RestController
@RequestMapping("/ops/do")
public class OpsAgentController extends BaseController
{
    @Autowired
    private IOpsAgentService opsDoService;

    /**
     * 查询运维待办管理列表
     */
    @PreAuthorize("@ss.hasPermi('ops:agent:list')")
    @GetMapping("/list")
    public TableDataInfo list(OpsAgent opsAgent)
    {
        startPage();
        List<OpsAgent> list = opsDoService.selectOpsDoList(opsAgent);
        return getDataTable(list);
    }
    /**
     * 查询运维待办管理列表
     */
    @PreAuthorize("@ss.hasPermi('ops:agent:list')")
    @GetMapping("/query")
    public AjaxResult query(OpsAgent opsAgent)
    {
        Long aLong = opsDoService.selectCount(opsAgent);
        List<OpsAgent> list = opsDoService.selectOpsDoList(opsAgent);
        AjaxResult ajax = new AjaxResult();
        ajax.put("total",aLong);
        ajax.put("data",list);
        return ajax;
    }
    /**
     * 导出运维待办管理列表
     */
    @PreAuthorize("@ss.hasPermi('ops:agent:export')")
    @Log(title = "运维待办管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OpsAgent opsAgent)
    {
        List<OpsAgentExport> list = opsDoService.selectOpsDoListExport(opsAgent);
        ExcelUtil<OpsAgentExport> util = new ExcelUtil<>(OpsAgentExport.class);
        util.exportExcel(response, list, "运维待办管理数据");
    }

    /**
     * 获取运维待办管理详细信息
     */
//    @PreAuthorize("@ss.hasPermi('ops:agent:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(opsDoService.selectOpsDoById(id));
    }

    /**
     * 新增运维待办管理
     */
    @PreAuthorize("@ss.hasPermi('ops:agent:add')")
    @Log(title = "运维待办管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OpsAgent opsAgent)
    {
        return toAjax(opsDoService.insertOpsDo(opsAgent));
    }

    /**
     * 修改运维待办管理
     */
    @PreAuthorize("@ss.hasPermi('ops:agent:edit')")
    @Log(title = "运维待办管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OpsAgent opsAgent)
    {
        return toAjax(opsDoService.updateOpsDo(opsAgent));
    }

    /**
     * 删除运维待办管理
     */
    @PreAuthorize("@ss.hasPermi('ops:agent:remove')")
    @Log(title = "运维待办管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(opsDoService.deleteOpsDoByIds(ids));
    }

    /**
     * 运维待办管理处理
     * @param opsAgent
     * @return
     */
    @PreAuthorize("@ss.hasPermi('ops:agent:handle')")
    @Log(title = "运维待办管理", businessType = BusinessType.UPDATE)
    @PostMapping("/handle")
    public AjaxResult handle(@RequestBody OpsAgent opsAgent){
        return opsDoService.handle(opsAgent);
    }

    /**
     * 批量处理接口
     */
    @PreAuthorize("@ss.hasPermi('ops:agent:batch')")
    @Log(title = "运维待办管理", businessType = BusinessType.UPDATE)
    @PostMapping("/batchHandle")
    public AjaxResult batchHandle(@RequestBody BatchDto batchDto){
        return opsDoService.batchHandle(batchDto.getIds(),batchDto.getOpinion());
    }
}
