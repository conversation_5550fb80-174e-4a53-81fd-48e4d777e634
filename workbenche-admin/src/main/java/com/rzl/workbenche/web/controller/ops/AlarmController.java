package com.rzl.workbenche.web.controller.ops;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rzl.workbenche.ops.domain.Alarm;
import com.rzl.workbenche.ops.domain.dto.BatchDto;
import com.rzl.workbenche.ops.domain.export.AlarmExport;
import com.rzl.workbenche.ops.service.IAlarmService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.rzl.workbenche.common.annotation.Log;
import com.rzl.workbenche.common.core.controller.BaseController;
import com.rzl.workbenche.common.core.domain.AjaxResult;
import com.rzl.workbenche.common.enums.BusinessType;

import com.rzl.workbenche.common.utils.poi.ExcelUtil;
import com.rzl.workbenche.common.core.page.TableDataInfo;

/**
 * 运维告警管理Controller
 *
 * <AUTHOR>
 * @date 2023-08-23
 */
@RestController
@RequestMapping("/ops/alarm")
public class AlarmController extends BaseController
{
    @Autowired
    private IAlarmService alertService;

    /**
     * 查询运维告警管理列表
     */
    @PreAuthorize("@ss.hasPermi('ops:alarm:list')")
    @GetMapping("/list")
    public TableDataInfo list(Alarm alarm)
    {
        startPage();
        List<Alarm> list = alertService.selectAlarmList(alarm);
        return getDataTable(list);
    }
    /**
     * 查询运维告警管理列表(不分页)
     */
    @PreAuthorize("@ss.hasPermi('ops:alarm:list')")
    @GetMapping("/query")
    public AjaxResult query(Alarm alarm)
    {
        List<Alarm> list = alertService.selectAlarmList(alarm);
        AjaxResult ajax = new AjaxResult();
        ajax.put("total",list.size());
        ajax.put("data",list);
        return ajax;
    }

    /**
     * 导出运维告警管理列表
     */
    @PreAuthorize("@ss.hasPermi('ops:alarm:export')")
    @Log(title = "运维告警管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Alarm alarm)
    {
        List<AlarmExport> list = alertService.selectAlarmListExport(alarm);
        ExcelUtil<AlarmExport> util = new ExcelUtil<>(AlarmExport.class);
        util.exportExcel(response, list, "运维告警管理数据");
    }

    /**
     * 获取运维告警管理详细信息
     */
//    @PreAuthorize("@ss.hasPermi('ops:alarm:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(alertService.selectAlarmById(id));
    }

    /**
     * 新增运维告警管理
     */
    @PreAuthorize("@ss.hasPermi('ops:alarm:add')")
    @Log(title = "运维告警管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Alarm alarm)
    {
        return toAjax(alertService.insertAlarm(alarm));
    }

    /**
     * 修改运维告警管理
     */
    @PreAuthorize("@ss.hasPermi('ops:alarm:edit')")
    @Log(title = "运维告警管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Alarm alarm)
    {
        return toAjax(alertService.updateAlarm(alarm));
    }

    /**
     * 删除运维告警管理
     */
    @PreAuthorize("@ss.hasPermi('ops:alarm:remove')")
    @Log(title = "运维告警管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(alertService.deleteAlarmByIds(ids));
    }

    /**
     * 处理运维告警管理
     * @param alarm
     * @return
     */
    @PreAuthorize("@ss.hasPermi('ops:alarm:handle')")
    @Log(title = "运维告警管理", businessType = BusinessType.UPDATE)
    @PostMapping("/handle")
    public AjaxResult handle(@RequestBody Alarm alarm){
        try {
            return alertService.handle(alarm);
        } catch (Exception e) {
            return AjaxResult.error("系统繁忙，请稍后再时");
        }
    }

    /**
     * 批量处理接口
     */
    @PreAuthorize("@ss.hasPermi('ops:alarm:batch')")
    @Log(title = "运维告警管理", businessType = BusinessType.UPDATE)
    @PostMapping("/batchHandle")
    public AjaxResult batchHandle(@RequestBody BatchDto batchDto){
        return alertService.batchHandle(batchDto.getIds(),batchDto.getOpinion());
    }
}
