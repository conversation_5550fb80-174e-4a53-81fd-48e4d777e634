<?xml version="1.0"?>
<!DOCTYPE mycat:schema SYSTEM "schema.dtd">
<mycat:schema xmlns:mycat="http://io.mycat/">

	<schema name="gxdb" checkSQLschema="false" dataNode="dbGX"></schema>
	<schema name="shdb" checkSQLschema="false" dataNode="dbSH"></schema>
	<schema name="yndb" checkSQLschema="false" dataNode="dbYN"></schema>
	<schema name="gxpdb" checkSQLschema="false" dataNode="dbGXP"></schema>
	<schema name="gxbpdb" checkSQLschema="false" dataNode="dbGXBP"></schema>
	<!-- <dataNode name="dn1$0-743" dataHost="localhost1" database="db$0-743"
		/> -->
	<dataNode name="dbGX" dataHost="localGX" database="ops_workbenche_guangxi" />
	<dataNode name="dbSH" dataHost="localSH" database="ops_workbenche_shanghai" />
	<dataNode name="dbYN" dataHost="localYN" database="ops_workbenche_yunnan" />
	<dataNode name="dbGXP" dataHost="localGXP" database="ops_workbenche_guangxip" />
	<dataNode name="dbGXBP" dataHost="localGXBP" database="ops_workbenche" />

	<!--<dataNode name="dn4" dataHost="sequoiadb1" database="SAMPLE" />
	 <dataNode name="jdbc_dn1" dataHost="jdbchost" database="db1" />
	<dataNode	name="jdbc_dn2" dataHost="jdbchost" database="db2" />
	<dataNode name="jdbc_dn3" 	dataHost="jdbchost" database="db3" /> -->
	<dataHost name="localGX" maxCon="400" minCon="10" balance="0"
                          writeType="0" dbType="mysql" dbDriver="native" switchType="-1"  slaveThreshold="100">
			<heartbeat>select user()</heartbeat>
			<!-- can have multi write hosts -->
			<writeHost host="hostMaster1" url="i6572b308aebad1726f64938c-mysql.workbenche:3306" user="root" password="LuTt@20191001">
			</writeHost>
	</dataHost>
	<dataHost name="localSH" maxCon="400" minCon="10" balance="0"
                          writeType="0" dbType="mysql" dbDriver="native" switchType="-1"  slaveThreshold="100">
			<heartbeat>select user()</heartbeat>
			<!-- can have multi write hosts -->
			<writeHost host="hostMaster1" url="i6572b308aebad1726f64938c-mysql.workbenche:3306" user="root" password="LuTt@20191001">
			</writeHost>
	</dataHost>
	<dataHost name="localYN" maxCon="400" minCon="10" balance="0"
                          writeType="0" dbType="mysql" dbDriver="native" switchType="-1"  slaveThreshold="100">
			<heartbeat>select user()</heartbeat>
			<!-- can have multi write hosts -->
			<writeHost host="hostMaster1" url="i6572b308aebad1726f64938c-mysql.workbenche:3306" user="root" password="LuTt@20191001">
			</writeHost>
	</dataHost>
	<dataHost name="localGXP" maxCon="400" minCon="10" balance="0"
                          writeType="0" dbType="mysql" dbDriver="native" switchType="-1"  slaveThreshold="100">
			<heartbeat>select user()</heartbeat>
			<!-- can have multi write hosts -->
			<writeHost host="hostMaster1" url="i6572b308aebad1726f64938c-mysql.workbenche:3306" user="root" password="LuTt@20191001">
			</writeHost>
	</dataHost>
	<dataHost name="localGXBP" maxCon="400" minCon="10" balance="0"
                          writeType="0" dbType="mysql" dbDriver="native" switchType="-1"  slaveThreshold="100">
			<heartbeat>select user()</heartbeat>
			<!-- can have multi write hosts -->
			<writeHost host="hostMaster1" url="i6572b308aebad1726f64938c-mysql.workbenche:3306" user="root" password="LuTt@20191001">
			</writeHost>
	</dataHost>
</mycat:schema>
