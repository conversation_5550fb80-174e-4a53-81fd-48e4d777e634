package com.rzl.workbenche.common.utils;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.rzl.workbenche.common.exception.ServiceException;

import java.util.List;

/**
 * 参数解析工具类
 */

public class ParamParse {
    private ParamParse() {
    }

    private static final String  OPS_PRI = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAKEa8pkbupbZpCxhgAolJ0IWd+tkFreII9yaL/FUx5LVQlbzQNpw/RtVjJ2YM7E0a0Ch9G71sFs0svMeyuC7nImZF+i1GXXnGYjSBEK5Wx/FBz8IdVRssUa0BNU4u5j497QJrrMUbheHsE7/vZ79mBTBH2PsctZB88aGH5/7I/bfAgMBAAECgYB+VMJN25eaePorLx2XVwEM6s+Ij0jdDG2KrIHRzXheJMrLoh6F7LodeB0260YFvCQqX5VseU6chpuY19mhFp0tbGimorZDM8EvUkPLihZEPTv831Zq8/apWHzF4/kUcYHaSMOvSXAj7VRP8qUiunFlplG6Z7hD5BH2hZaS8j99yQJBAORB8zt8HmEkwBA2qUe38Vsu3dmkhlif0D/qztqnlAHBUyIVKKP75xcCnHVNqCh7PmPxFKZnLf9s0EP7cecHFRsCQQC0r5tTFXgckofFsCPob2rzO0j90mwpaRghMLu3nUUyuA0DuZnWEJixoo6+mOytbhgBapTJfKH8kaOftjewSHWNAkAQh54frqtciZbHFc5IfU+jNM+oFTwNavVfy5dTSlNzlRZ6H2IkDff8OJov/IGy/MnV3v2J12sDVlP2uFzVSDQFAkEAjj4EsFahdRTh7/4ndo9oCc2tO6zQ25TRmydrUDRuSmxcSodtlPkBzC3l5CQthqa6HTtToH8OYvAgeNYRZyTT7QJAU9Oz6f6CuIaDtjMm/AludsNHo9Wb3K+4U0XW2JcTtU6slYsX0NB2ni5NEjg75qbDefd78fyHHbrjxubC26zCPg==";

    public static <T> List<T> parseClass(String data, Class<T> classOfT) {
        String jsonArray = "";
        //解密参数
        try {
            jsonArray = RSAUtil.decrypt(data, OPS_PRI);
            if (!jsonArray.contains("[")){
                jsonArray = convertToJsonArrayString(jsonArray);
            }
            return JSON.parseArray(jsonArray, classOfT);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    public static String convertToJsonArrayString(String jsonString) {
        try {
            // 创建ObjectMapper对象
            ObjectMapper objectMapper = new ObjectMapper();

            // 解析JSON字符串为JSON对象
            ObjectNode jsonObject = objectMapper.readValue(jsonString, ObjectNode.class);

            // 创建JSON数组
            ArrayNode jsonArray = objectMapper.createArrayNode();

            // 将JSON对象添加到数组中
            jsonArray.add(jsonObject);

            // 将JSON数组转换为字符串
            return jsonArray.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    public static String parseStr(String data){
        return RSAUtil.decrypt(data, OPS_PRI).toString();
    }
}
