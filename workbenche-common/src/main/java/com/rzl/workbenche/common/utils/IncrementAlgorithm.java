package com.rzl.workbenche.common.utils;

import org.springframework.stereotype.Component;
import java.util.List;

@Component
public class IncrementAlgorithm {
    private static final String PREFIX = "A";
    private static final int START_NUMBER = 1;

    private int currentNumber;

    public IncrementAlgorithm() {
        this.currentNumber = START_NUMBER;
    }

    public String getNextId() {
        String numberString = String.format("%04d", currentNumber);
        String id = PREFIX + numberString;
        currentNumber++;
        return id;
    }

    public String getSysId(List<String> list){
        String id = "";
        do {
            id = getNextId();
        }while (list.contains(id));
        return id;
    }
}
