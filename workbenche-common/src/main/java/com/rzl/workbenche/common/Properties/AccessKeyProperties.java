package com.rzl.workbenche.common.Properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@ConfigurationProperties(prefix = "sign")
public class AccessKeyProperties {
    /**
     * 接口签名验证超时时间
     */
    private long expireTime;
    /**
     * 接口签名唯一密钥
     */
    private String privateKey;
    /**
     * 接口appKey
     */
    private List<String> accessKeys;

    public long getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(long expireTime) {
        this.expireTime = expireTime;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public List<String> getAccessKeys() {
        return accessKeys;
    }

    public void setAccessKeys(List<String> accessKeys) {
        this.accessKeys = accessKeys;
    }
}
