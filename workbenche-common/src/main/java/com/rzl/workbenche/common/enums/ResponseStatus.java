package com.rzl.workbenche.common.enums;


/**
 * 响应枚举类
 *
 * <AUTHOR>
 */
public enum ResponseStatus {

    OK(200, "操作成功"),

    ERROR(500, "失败"),

    SERVICEERROR(10002, "服务暂停"),

    SIGNERROR(10011, "签名错误"),

    PARAMERROR(10010,"请求参数错误");

    private final int code;

    private final String info;

    ResponseStatus(int code, String info) {
        this.code = code;
        this.info = info;
    }

    public int getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
