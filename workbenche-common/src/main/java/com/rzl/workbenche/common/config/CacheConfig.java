package com.rzl.workbenche.common.config;

import com.rzl.workbenche.common.utils.StrUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class CacheConfig {

    private static Map<String,String> map = new ConcurrentHashMap<>();
    static {
        map.put("110000","bjdb");
        map.put("120000","tjdb");
        map.put("130000","hedb");
        map.put("140000","sxdb");
        map.put("150000","nmdb");
        map.put("210000","lndb");
        map.put("220000","jldb");
        map.put("230000","hldb");
        map.put("310000","shdb");
        map.put("320000","jsdb");
        map.put("330000","zjdb");
        map.put("340000","ahdb");
        map.put("350000","fjdb");
        map.put("360000","jxdb");
        map.put("370000","sddb");
        map.put("410000","hadb");
        map.put("420000","hbdb");
        map.put("430000","hndb");
        map.put("440000","gddb");
        map.put("450000","gxdb");
        map.put("450001","gxpdb");
        map.put("460000","hidb");
        map.put("500000","cqdb");
        map.put("510000","scdb");
        map.put("520000","gzdb");
        map.put("530000","yndb");
        map.put("540000","xzdb");
        map.put("610000","sndb");
        map.put("620000","gsdb");
        map.put("630000","qhdb");
        map.put("640000","nxdb");
        map.put("650000","xjdb");
        map.put("710000","twdb");
        map.put("000000","gxbpdb");
    }

    public String getMap(String param) {

        return map.get(param);
    }

    public static void setMap(Map<String, String> map) {
        CacheConfig.map = map;
    }

    public static Map<String, String> getMap() {
        return map;
    }

    /**
     * 通过key获取value
     * @param
     * @return
     */
    public static void setDb(String domain){
        TenantContextHolder.setTenant(StrUtils.isEmpty(domain) ? "gxbpdb" : map.get(domain));
    }
    public static String getPrg(String value){
        return value.substring(0,2).toUpperCase();
    }

    /**
     * 通过value获取key
     */
    public static String getDomain(String value) {
        for (Map.Entry<String, String> entry : map.entrySet()) {
            if (entry.getValue().equals(value)) {
                return entry.getKey();
            }
        }
        return "";
    }

}
